import { TPlotData, TPlotSettings } from "../types/plotTypes";

export abstract class BasePlotStore<T> {
    protected srcData: T;
    readonly id: string;
    private settings?: TPlotSettings;
    readonly type!: string;
    readonly name?: string;

    constructor(plotData: TPlotData) {
        this.id = plotData.id
        this.srcData = plotData.data as T
        this.settings = plotData.settings
        this.name = plotData.name
    }

    get showPriceLine() {
        return this.settings?.style.showPriceLine ?? false
    }
    set showPriceLine(value: boolean) {
        if (!this.settings) {
            this.settings = {
                style: {
                    showPriceLine: value
                }
            }
        } else {
            this.settings.style.showPriceLine = value
        }
    }
    abstract get priceLineValue(): number | undefined | null
}