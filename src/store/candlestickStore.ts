
import { type TCandlestick } from "../types/plotTypes";
import { ListStore } from "./listStore";

export class CandlestickStore extends ListStore<TCandlestick> {
    readonly type = 'candlestick'
    getValueRangeByTime(fromX: number, toX: number) {
        const selected = this.selectByTime(fromX, toX);
        if (selected.length === 0) return

        let max = -Infinity;
        let min = Infinity;
        for (const dp of selected) {
            if (dp.high > max) max = dp.high;
            if (dp.low < min) min = dp.low;
        }
        return { max, min };
    }
    get priceLineValue() {
        return this.last.close
    }
}