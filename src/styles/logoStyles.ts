import { styled, keyframes } from '@stitches/react';
import { LiquidGlass } from './commonStyles';

// Define the wave animation
const waveAnimation = keyframes({
    '0%, 100%': { transform: 'translateY(0)' },
    '40%': { transform: 'translateY(-5px)' }
});

// Styled component for individual characters
export const LogoChar = styled('span', {
    display: 'inline-block',
    animation: `${waveAnimation} 2.5s ease-in-out infinite`,

    // Variants for different animation delays
    variants: {
        position: {
            0: { animationDelay: '0s' },
            1: { animationDelay: '0.3s' },
            2: { animationDelay: '0.6s' },
            3: { animationDelay: '0.9s' },
            4: { animationDelay: '1.2s' },
            5: { animationDelay: '1.5s' },
            6: { animationDelay: '1.8s' },
            7: { animationDelay: '2.1s' }
        }
    }
});

export const LogoContainer = styled('div', {
    position: 'relative',
    marginRight: '30px',
    marginLeft: '15px',
    width: '120px',
    height: '50px',
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    pointerEvents: 'none',
    userSelect: 'none',
});

export const LogoText = styled('div', {
    color: 'rgb(7, 81, 207)',
    fontSize: '20px',
    fontWeight: 'bold',
    fontFamily: 'Zed, sans-serif',
    position: 'relative',
    zIndex: 5,
    textAlign: 'center',
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    letterSpacing: '1px', // Add a small letter spacing for better wave visibility
});

// Concentric rectangles
export const ConcentricRect = styled('div', LiquidGlass, {
    position: 'absolute',
    top: '50%',
    left: '50%',
    transform: 'translate(-50%, -50%)',
    background: 'rgba(231, 237, 248, 0.02)',


    variants: {
        level: {
            1: {
                width: '100%',
                height: '100%',
            },
            2: {
                width: '85%',
                height: '85%',
                boxShadow: `-5px 0 5px -2px rgba(0, 0, 0, 0.08), 5px 0 5px -2px rgba(0, 0, 0, 0.08)`,
            },
            3: {
                width: '70%',
                height: '70%',
                boxShadow: `-5px 0 5px -2px rgba(0, 0, 0, 0.08), 5px 0 5px -2px rgba(0, 0, 0, 0.08)`,
            },
            4: {
                width: '55%',
                height: '55%',
                boxShadow: `-5px 0 5px -2px rgba(0, 0, 0, 0.08), 5px 0 5px -2px rgba(0, 0, 0, 0.08)`,
            }
        }
    }
});
