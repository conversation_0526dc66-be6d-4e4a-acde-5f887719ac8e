
import { styled, globalCss } from '@stitches/react'
import { XAXIS_HEIGHT, YAXIS_WIDTH } from '../commons/constants';
import { RoundedButton } from './commonStyles';

export const globalStyles = globalCss({
    // Prevent blue highlight on all elements
    '*': {
        WebkitTapHighlightColor: 'rgba(0,0,0,0)',
        outline: 'none',
    },

    // Prevent text selection on the entire app
    'body, html, #root': {
        userSelect: 'none',
        WebkitUserSelect: 'none',
        MozUserSelect: 'none',
        msUserSelect: 'none',
    },

    // Specific styles for Konva elements
    '.konvajs-content': {
        userSelect: 'none !important',
        WebkitUserSelect: 'none !important',
        MozUserSelect: 'none !important',
        msUserSelect: 'none !important',
        WebkitTapHighlightColor: 'rgba(0,0,0,0) !important',
        outline: 'none !important',

        '& canvas': {
            userSelect: 'none !important',
            WebkitUserSelect: 'none !important',
            MozUserSelect: 'none !important',
            msUserSelect: 'none !important',
            WebkitTapHighlightColor: 'rgba(0,0,0,0) !important',
            outline: 'none !important',
        }
    },

    // Prevent blue highlight on draggable elements
    '[draggable="true"]': {
        WebkitUserDrag: 'none',
        userDrag: 'none',
        WebkitTapHighlightColor: 'rgba(0,0,0,0)',
    }
});

export const AppContainer = styled('div', {
    position: 'relative',
    width: '100%',
    height: '100%',
    // overflow: 'hidden',
    userSelect: 'none',
    WebkitUserSelect: 'none',
    MozUserSelect: 'none',
    msUserSelect: 'none',
    WebkitTapHighlightColor: 'rgba(0,0,0,0)',

    display: 'flex',
    flexDirection: 'column'
})


export const Header = styled('div', {
    position: 'absolute',
    left: 0,
    top: 0,
    margin: 10,
    width: '100%',
    height: '10vh',
    maxHeight: 60,
    display: 'flex',
    pointerEvents: 'none',
    flexDirection: 'row',
    gap: '20px',
    paddingBottom: '5px',
    paddingTop: '5px',
    alignItems: 'center',
})



export const Footer = styled('div', {
    height: XAXIS_HEIGHT,
    width: '100%',
    display: 'flex',
    flexDirection: 'row',
    userSelect: 'none',
    WebkitUserSelect: 'none',
    MozUserSelect: 'none',
    msUserSelect: 'none',
    WebkitTapHighlightColor: 'rgba(0,0,0,0)',
    flexShrink: 0,
})

export const PaperBackground = styled('div', {
    position: 'absolute',
    top: 0,
    left: 0,
    width: '100%',
    height: '100%',
    backgroundColor: '#FEF3E2',
    backgroundImage: 'url("../src/assets/noise.png")',
    backgroundRepeat: 'repeat',
    backgroundSize: '0.3 0.3',
    opacity: 0.03,
})

export const BlueprintGrid = styled('div', {
    position: 'absolute',
    top: 0,
    left: 0,
    width: `calc(100% - ${YAXIS_WIDTH}px)`,
    height: `calc(100% - ${XAXIS_HEIGHT}px)`,
    backgroundSize: '20px 20px',
    backgroundImage: `
    linear-gradient(to right, rgba(30, 144, 255, 0.05) 1px, transparent 1px),
    linear-gradient(to bottom, rgba(30, 144, 255, 0.05) 1px, transparent 1px)
  `,
    userSelect: 'none',
    WebkitUserSelect: 'none',
    MozUserSelect: 'none',
    msUserSelect: 'none',
    WebkitTapHighlightColor: 'rgba(0,0,0,0)',
    '&::after': {
        content: '""',
        position: 'absolute',
        left: 0,
        bottom: 0,
        width: '100%',
        height: '100%',
        background: 'linear-gradient(to top,rgba(215, 171, 89, 0.03), transparent)',
        pointerEvents: 'none',
    },
})

export const SnapshotButton = styled(RoundedButton, {
    position: 'fixed',
    bottom: '80px',
    right: '150px',
    width: '50px',
    height: '50px',
    borderRadius: '50%',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    cursor: 'pointer',
    zIndex: 1000,


});

export const CountdownContainer = styled('div', {
    width: '100%',
    textAlign: 'center',
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    padding: "6px",
    fontSize: "15px",
    fontWeight: 'bold',
    cursor: "pointer",
});
