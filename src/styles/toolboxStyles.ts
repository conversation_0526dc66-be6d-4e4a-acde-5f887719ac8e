import { styled } from '@stitches/react';
import { LiquidGlass, RoundedButton } from './commonStyles';

export const ToolboxContainer = styled('div', {
    position: 'absolute',
    left: '7%',
    top: '40%',
    transform: 'translateY(-50%)',
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    gap: '10px',
    padding: '10px',
    zIndex: 1000,
});

export const ToolboxMenuContainer = styled('div', LiquidGlass, {
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    gap: '10px',
    padding: '10px',
    zIndex: 1000,
    borderRadius: '12px'
});

export const ToolboxItem = styled('div', {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    width: '40px',
    height: '40px',
    borderRadius: '8px',
    padding: '5px',
    cursor: 'pointer',
    transition: 'background-color 0.2s ease',
    color: 'rgb(7, 81, 207)',

    '&:hover': {
        backgroundColor: 'rgba(7, 81, 207, 0.15)',
    },

    variants: {
        active: {
            true: {
                backgroundColor: 'rgba(7, 81, 207, 0.2)',
            }
        }
    }
});

export const ToolboxTooltip = styled('div', {
    position: 'absolute',
    left: '100%',
    top: '50%',
    transform: 'translateY(-50%) translateX(-10px)',
    marginLeft: '15px',
    padding: '5px 8px',
    // backgroundColor: 'rgba(255, 255, 255, 0.9)',
    borderRadius: '4px',
    fontSize: '14px',
    fontWeight: 'bold',
    color: 'rgb(7, 81, 207)',
    // border: '1px solid rgba(7, 81, 207, 0.2)',
    pointerEvents: 'none',
    opacity: 0,
    transition: 'all 0.2s ease',
    whiteSpace: 'nowrap',
    zIndex: 1001,
});

export const ClearDrawingsButton = styled(RoundedButton, {
    position: 'absolute',
    bottom: '-50px',
    left: '50%',
    width: 35,
    height: 35,
    transform: 'translateX(-50%)',
});
