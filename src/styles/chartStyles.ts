import { styled } from "@stitches/react";
import { LiquidGlass } from "./commonStyles";

export const ChartGroupContainer = styled('div', {
    flex: 1,
    width: '100%',
    display: 'flex',
    flexDirection: 'column',
    userSelect: 'none',
    WebkitUserSelect: 'none',
    MozUserSelect: 'none',
    msUserSelect: 'none',
    WebkitTapHighlightColor: 'rgba(0,0,0,0)',
    overflow: 'hidden',
})

export const ChartContainer = styled('div', {
    display: 'flex',
    flexDirection: 'row',
    position: 'relative',
    width: '100%',
    height: '100%',
    userSelect: 'none',
    WebkitUserSelect: 'none',
    MozUserSelect: 'none',
    msUserSelect: 'none',
    WebkitTapHighlightColor: 'rgba(0,0,0,0)',
});

export const YAxisContainer = styled('div', {
    position: 'absolute',
    right: 0,
    backdropFilter: 'blur(3px)',
    radius: '0px',

    background: 'linear-gradient(to right, rgba(255, 255, 255, 0.86) 0%, rgba(7, 81, 207, 0.02) 20%, rgba(7, 81, 207, 0.02) 100%)',
    borderLeft: '1px solid rgba(7, 80, 207, 0.08)',
    boxShadow: `none`,

    '&::after': {
        content: '""',
        background: 'none'
    },
});

