import { css, styled } from "@stitches/react";

export const LiquidGlass = css({
    backdropFilter: 'blur(3px)',
    background: 'rgba(248, 249, 249, 0.15)',
    border: '1px solid rgba(255, 255, 255, 0.46)',
    boxShadow: `1px 2px 10px 1px rgba(0, 0, 0, 0.08)`,
    borderRadius: 'inherit',
})


export const RoundedButton = styled('div', LiquidGlass, {
    transform: 'translateX(-50%)',
    transformOrigin: 'center center', // ✅ here
    width: '50px',
    height: '50px',
    borderRadius: '50%',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    cursor: 'pointer',
    zIndex: 1000,
    transition: 'all 0.2s ease',
    willChange: 'transform',
    color: 'rgb(7, 81, 207)',

    '&:hover': {
        transform: 'translateX(-50%) scale(1.05)',
        boxShadow: '0 4px 15px rgba(0, 0, 0, 0.15)',
    },

    '&:active': {
        transform: 'translateX(-50%) scale(0.95)',
    },

    variants: {
        type: {
            primary: {
                color: 'rgb(7, 81, 207)',
                backgroundColor: 'rgba(7, 81, 207,  0.02)',
            },
            danger: {
                color: 'rgb(254, 38, 142)',
                backgroundColor: 'rgba(254, 38, 142, 0.02)',
            },
        },
    },
    defaultVariants: {
        type: 'primary',
    },
});
