import { keyframes, styled } from '@stitches/react';
import { LiquidGlass } from './commonStyles';


const expand = keyframes({
    '0%': { transform: 'scale(0)', opacity: 0 },
    '100%': { transform: 'scale(1)', opacity: 1 },
});


export const ContextMenuContainer = styled('div', LiquidGlass, {
    position: 'absolute', // Use absolute positioning to position relative to the chart container
    display: 'flex',
    flexDirection: 'column',
    zIndex: 9999, // Very high z-index to ensure it's on top of everything
    overflow: 'hidden',
    minWidth: '200px',
    userSelect: 'none',
    borderRadius: '12px',
    animation: `${expand} 0.2s ease-out forwards`,
    // Ensure transform origin is set to start from click position
    transformOrigin: 'top left',
});

export const ContextMenuItem = styled('div', {

    fontSize: '14px',
    color: 'hsl(218, 93.50%, 42.00%)',
    cursor: 'pointer',
    transition: 'all 0.1s ease',
    display: 'flex',
    alignItems: 'center',
    gap: '8px',
    fontWeight: 'bold',

    '&:hover': {
        backgroundColor: 'rgba(7, 81, 207, 0.15)',
    },

    '&:active': {
        backgroundColor: 'rgba(7, 81, 207, 0.25)',
    },

    variants: {
        type: {
            default: {
                padding: '10px 14px',
            },
            action: {
                padding: '8px 14px',
                fontSize: '12px',
            }
        },
        selected: {
            true: {
                backgroundColor: 'rgba(7, 81, 207, 0.2)',
                color: 'rgb(7, 81, 207)',
                fontWeight: 'bold',
            }
        }

    },
    defaultVariants: {
        type: 'default',
    }
});

export const ContextMenuDivider = styled('div', {
    height: '1px',
    backgroundColor: 'rgba(7, 81, 207, 0.2)',
});
