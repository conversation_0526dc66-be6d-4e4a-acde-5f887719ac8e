import { styled } from '@stitches/react';
import { keyframes } from '@stitches/react';
import { LiquidGlass } from './commonStyles';

const twistAnimation = keyframes({
    '0%': { transform: 'rotate(0deg)' },
    '25%': { transform: 'rotate(1deg)' },
    '75%': { transform: 'rotate(-1deg)' },
    '100%': { transform: 'rotate(0deg)' }
});

// Spinner animation for loading state
const spinAnimation = keyframes({
    '0%': { transform: 'rotate(0deg)' },
    '100%': { transform: 'rotate(360deg)' }
});

// Pulse animation for loading state
const pulseAnimation = keyframes({
    '0%': { opacity: 1 },
    '50%': { opacity: 0.6 },
    '100%': { opacity: 1 }
});

// Position drawer styles
const slideDown = keyframes({
    '0%': { opacity: 0, transform: 'translateY(-20px)', maxHeight: '0' },
    '100%': { opacity: 1, transform: 'translateY(0)', maxHeight: '150px' },
});

const slideUp = keyframes({
    '0%': { opacity: 1, transform: 'translateY(0)', maxHeight: '150px' },
    '100%': { opacity: 0, transform: 'translateY(-20px)', maxHeight: '0' },
});

// Main outer container that holds both the panel and positions list
export const TradingPanelContainer = styled('div', {
    position: 'absolute',
    zIndex: 1000,
    userSelect: 'none',
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    gap: '0',
    minWidth: '220px',
});

// New container for floating buttons
export const FloatingButtonsContainer = styled('div', {
    position: 'absolute',
    top: '1px',
    right: '-40px', // Positions buttons to the right of the panel
    display: 'flex',
    flexDirection: 'column',
    gap: '3px',
    zIndex: 1001, // Ensures buttons are above other elements
});


// The actual trading panel
export const TradingPanelMainContainer = styled('div', LiquidGlass, {
    padding: '8px',
    minWidth: '180px',
    width: '100%',
    position: 'relative', // Allows absolute positioning of child elements
    zIndex: 1000,
    borderRadius: '16px',
});


export const TradingPanelHeader = styled('div', {
    display: 'flex',
    justifyContent: 'flex-start',
    alignItems: 'center',
    marginBottom: '8px',
    color: 'rgb(7, 81, 207)',
    fontWeight: 'bold',
    fontSize: '15px',
    paddingBottom: 5,
    fontFamily: 'Zed, sans-serif',
    cursor: 'grab',

    '&:active': {
        cursor: 'grabbing',
    },

    '&::before': {
        content: '⋮⋮',
        marginRight: '6px',
        fontSize: '10px',
        opacity: 0.5,
    },

    '& > div:first-child': {
        flex: '1',
    },
});

export const InputContainer = styled('div', {
    marginBottom: '8px',
});

export const AmountInput = styled('input', {
    width: '100%',
    padding: '6px 8px',
    borderRadius: '12px',
    border: '1px solid rgba(7, 81, 207, 0.3)',
    backgroundColor: 'rgba(255, 255, 255, 0.7)',
    color: 'rgb(7, 81, 207)',
    fontSize: '12px',
    fontWeight: 'bold',
    outline: 'none',
    transition: 'all 0.2s ease',

    '&:focus': {
        borderColor: 'rgba(7, 81, 207, 0.6)',
        boxShadow: '0 0 0 2px rgba(7, 81, 207, 0.1)',
    },

    '&::placeholder': {
        color: 'rgba(7, 81, 207, 0.1)',
    },
});

export const ButtonsContainer = styled('div', {
    display: 'flex',
    flexDirection: 'row', // Changed to vertical arrangement
    gap: '8px',
});

export const ActionButton = styled('button', {
    flex: 1,
    padding: '6px',
    borderRadius: '12px',
    // borderWidth: '0.2px',
    border: '1px solid rgba(255, 255, 255, 0.06)',
    backdropFilter: 'blur(10px)',
    fontWeight: 'bold',
    fontSize: '10px',
    cursor: 'pointer',
    transition: 'all 0.2s ease',
    fontFamily: 'Zed, sans-serif',
    position: 'relative',
    minWidth: '33px',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',

    '&:hover': {
        animation: `${twistAnimation} 0.5s ease`,
    },

    '&:disabled': {
        cursor: 'not-allowed',
        opacity: 0.7,
    },

    variants: {
        action: {
            buy: {
                backgroundColor: 'rgba(3, 167, 145, 0.2)',
                color: '#03A791',
                // borderColor: 'rgba(3, 167, 145, 0.1)',

                '&:hover': {
                    backgroundColor: 'rgba(3, 167, 145, 0.4)',
                },
            },
            sell: {
                backgroundColor: 'rgba(255, 125, 41, 0.2)',
                color: '#FF7D29',
                // borderColor: 'rgba(255, 125, 41, 0.1)',

                '&:hover': {
                    backgroundColor: 'rgba(255, 125, 41, 0.4)',
                },
            },
            reverse: {
                backgroundColor: 'rgba(252, 241, 89, 0.37)',
                color: '#FFC107',
                // borderColor: 'rgba(255, 193, 7, 0.21)',

                '&:hover': {
                    backgroundColor: 'rgba(252, 233, 89, 0.8)',
                },
            },
            close: {
                backgroundColor: 'rgba(7, 81, 207, 0.1)',
                color: 'rgb(7, 81, 207)',
                borderColor: 'rgba(7, 81, 207, 0.2)',
                padding: '2px 4px',
                fontSize: '9px',
                minWidth: '40px',
                flex: '0 0 auto',

                '&:hover': {
                    backgroundColor: 'rgba(7, 81, 207, 0.2)',
                },
            },
        },
        loading: {
            true: {
                cursor: 'wait',
                animation: `${pulseAnimation} 1.5s infinite`,
                color: 'transparent !important', // Force override with !important
                pointerEvents: 'none',

                '&::before': {
                    content: '""',
                    position: 'absolute',
                    width: '0.6rem',
                    height: '0.6rem',
                    border: '2px solid rgba(85, 98, 120, 0.5)',
                    borderTopColor: 'rgb(85, 98, 120)',
                    borderRadius: '50%',
                    animation: `${spinAnimation} 0.8s linear infinite`,
                },
            },
        },
    },
});


export const PositionsContainer = styled('div', LiquidGlass, {
    width: '90%',
    borderRadius: '0 0 12px 12px',
    background: 'rgba(229, 232, 251, 0.63)',
    borderTop: 'none',
    overflow: 'hidden',
    zIndex: 200,
    marginTop: '-2px',
    paddingTop: '2px',
    alignSelf: 'center',
    boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',

    variants: {
        open: {
            true: {
                animation: `${slideDown} 0.3s ease forwards`,
                opacity: 1
            },
            false: {
                animation: `${slideUp} 0.3s ease forwards`,
                opacity: 0
            },
        },
    },

    defaultVariants: {
        open: false,
    },
});

export const PositionsList = styled('div', {
    display: 'flex',
    flexDirection: 'column',
    gap: '4px',
    // maxHeight: '120px',
    overflowY: 'auto',
    padding: '10px',

    '&::-webkit-scrollbar': {
        width: '3px',
    },
    '&::-webkit-scrollbar-track': {
        background: 'rgba(7, 81, 207, 0.05)',
    },
    '&::-webkit-scrollbar-thumb': {
        background: 'rgba(7, 81, 207, 0.2)',
        borderRadius: '2px',
    },
});

export const PositionItem = styled('div', {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: '6px',
    borderRadius: '10px',
    fontSize: '11px',
    backgroundColor: 'rgba(255, 255, 255, 0.77)',
    gap: '8px',

    variants: {
        side: {
        buy: {
                borderLeft: '3px solid #03A791',
            },
            sell: {
                borderLeft: '3px solid #e74c3c',
            },
        },
    },

    '&:hover': {
        cursor: 'pointer',
    },
});

export const PositionDetails = styled('div', {
    display: 'flex',
    flexDirection: 'column',
    gap: '2px',
    flex: '1 1 auto',
    minWidth: 0,
});

export const PositionSymbol = styled('div', {
    fontWeight: 'bold',
    color: 'rgb(7, 81, 207)',
    whiteSpace: 'nowrap',
    overflow: 'hidden',
    textOverflow: 'ellipsis',
    display: 'flex',
    alignItems: 'center',
    gap: '4px',
});

export const PositionAmount = styled('div', {
    fontSize: '10px',
    color: 'rgba(7, 80, 207, 0.48)',
    whiteSpace: 'nowrap',
    overflow: 'hidden',
    textOverflow: 'ellipsis',
    display: 'flex',
    alignItems: 'center',
    gap: '4px',
});

export const PositionSide = styled('span', {
    fontWeight: 'bold',
    fontSize: '9px',
    padding: '1px 3px',
    borderRadius: '2px',

    variants: {
        side: {
            long: {
                backgroundColor: 'rgba(3, 167, 145, 0.15)',
                color: '#03A791',
            },
            short: {
                backgroundColor: 'rgba(231, 76, 60, 0.15)',
                color: '#e74c3c',
            },
        },
    },
});

export const PositionPnl = styled('div', {
    fontSize: '10px',
    whiteSpace: 'nowrap',
    overflow: 'hidden',
    textOverflow: 'ellipsis',
    display: 'flex',
    alignItems: 'center',
    gap: '4px',
    fontWeight: 'bold',

    variants: {
        status: {
            positive: {
                color: '#03A791',
            },
            negative: {
                color: '#e74c3c',
            },
        },
    },
});

export const EmptyPositions = styled('div', {
    padding: '8px',
    textAlign: 'center',
    color: 'rgba(7, 81, 207, 0.5)',
    fontStyle: 'italic',
    fontSize: '11px',
});