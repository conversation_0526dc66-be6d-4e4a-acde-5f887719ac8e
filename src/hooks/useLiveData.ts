import { useEffect, useRef } from "react";
import { WEB_SOCKET_URL } from "../commons/constants";
import { useSignal } from "./useSignal";
import { TSignal, TTicker } from "../types/commonTypes";
import { TPosition } from "../types/tradingTypes";
import { ChartStore } from "../store/chartStore";
import { useAppContext, } from "../contexts/contexts";

export const useLiveData = (chartStoreMap: Map<string, ChartStore> | null, newerExhausted: TSignal<boolean>) => {
    const { symbol, timeframe } = useAppContext()
    const ticker = useSignal<TTicker | null>(null);
    const positions = useSignal<TPosition[]>([]);
    const wsRef = useRef<WebSocket | null>(null);
    newerExhausted.use()

    useEffect(() => {
        if (!newerExhausted.value || !chartStoreMap) return;

        wsRef.current = new WebSocket(
            `${WEB_SOCKET_URL}?topic=chartdata:${symbol}:${timeframe},ticker:${symbol}:${timeframe},position`
        );

        wsRef.current.onmessage = (event) => {
            if (!chartStoreMap || !chartStoreMap.size) return;
            const payload = JSON.parse(event.data);

            switch (payload.topic) {
                case `chartdata:${symbol}:${timeframe}`:
                    if (payload.updates && Array.isArray(payload.updates)) {
                        for (const streamData of payload.updates) {
                            for (const chartStore of chartStoreMap.values()) {
                                const result = chartStore.updateData(streamData);
                                if (result) break;
                            }
                        }
                    }
                    break;
                case `ticker:${symbol}:${timeframe}`:
                    if (
                        payload.ticker &&
                        (!ticker.value ||
                            payload.ticker.time > ticker.value.time ||
                            (payload.ticker.time === ticker.value.time && payload.ticker.price !== ticker.value.price))
                    ) {
                        ticker.value = payload.ticker;
                    }
                    break;
                case "position":
                    if (payload.positions && Array.isArray(payload.positions)) {
                        positions.value = payload.positions;
                    }
                    break;
            }
        };

        return () => {
            if (wsRef.current) {
                wsRef.current.close()
                wsRef.current = null;
            }
        };

    }, [newerExhausted.value, chartStoreMap])

    return { ticker, positions };
};