import { useEffect, useState, } from "react";
import { fetchAlertConfigs, fetchChartDataList, fetchDrawingConfigs } from "../commons/api";
import { MAIN_CHART_NAME } from "../commons/constants";

import { useSignal } from "./useSignal";
import { TChartData } from "../types/plotTypes";
import { TDrawingConfig } from "../types/toolboxTypes";
import { ChartStore } from "../store/chartStore";

import { useAppContext, useNotificationContext } from "../contexts/contexts";
import { debounce } from "../commons/util";
import { TAlertConfig } from "../types/alertTypes";


const debouncedFetch = debounce(fetchChartDataList, 500);

export const useChartData = () => {
    const { error } = useNotificationContext();
    const { symbol, timeframe, timeRange, timeUnit } = useAppContext()
    const [chartStoreMap, setChartStoreMap] = useState<Map<string, ChartStore> | null>(null);
    const isLoadingData = useSignal(false);
    const newerExhausted = useSignal(false);
    const olderExhausted = useSignal(false);
    const drawingConfigList = useSignal<TDrawingConfig[]>([]);
    const alertConfigList = useSignal<TAlertConfig[]>([]);

    timeRange.use()

    // Fetch initial chart data
    useEffect(() => {

        isLoadingData.value = true;
        fetchChartDataList({ symbol, timeframe, startTime: timeRange.value.min, endTime: timeRange.value.max })
            .then((data: TChartData[]) => {
                const newMap = new Map<string, ChartStore>();
                for (const chartData of data) {
                    newMap.set(chartData.name, new ChartStore(chartData));
                }
                setChartStoreMap(newMap);

                if (timeRange.value.max + timeUnit > Date.now()) newerExhausted.value = true;
                else newerExhausted.value = false;
            })
            .catch((e) => {
                error({ title: "Failed to fetch chart data", message: e.message });
            })
            .finally(() => {
                isLoadingData.value = false;
            });

        return () => {
            isLoadingData.value = true;
            olderExhausted.value = false;
            chartStoreMap?.clear()
        };
    }, [symbol, timeframe]);

    // Fetch drawing configs
    useEffect(() => {
        fetchDrawingConfigs({ symbol }).then((data) => {
            drawingConfigList.value = data;
        });
    }, [symbol]);

    useEffect(() => {
        fetchAlertConfigs().then((data) => {
            alertConfigList.value = data;
        });
    }, []);



    // Watch timeRange changes
    useEffect(() => {
        if (!chartStoreMap || isLoadingData.value) return;

        const mainChartTimeRange = chartStoreMap.get(MAIN_CHART_NAME)?.getTimeRange();
        if (!mainChartTimeRange) return;

        if (mainChartTimeRange.max + timeUnit > Date.now()) {
            newerExhausted.value = true;
        } else if (mainChartTimeRange.max < timeRange.value.max) {
            console.log("fetching newer data");
            isLoadingData.value = true;
            debouncedFetch({ symbol, timeframe, startTime: mainChartTimeRange.max + 1, limit: 200 })
                .then((data: TChartData[]) => {
                    for (const chartData of data) {
                        const chartStore = chartStoreMap.get(chartData.name);
                        if (chartStore) chartStore.appendData(chartData);
                    }
                })
                .finally(() => {
                    isLoadingData.value = false;
                });
        }




        if (mainChartTimeRange.min > timeRange.value.min + timeUnit && !olderExhausted.value) {
            console.log("fetching older data");
            isLoadingData.value = true;
            debouncedFetch({ symbol, timeframe, endTime: mainChartTimeRange.min - 1, limit: 200 })
                .then((data: TChartData[]) => {
                    if (data.length === 0) olderExhausted.value = true;
                    for (const chartData of data) {
                        const chartStore = chartStoreMap.get(chartData.name);
                        if (chartStore) chartStore.prependData(chartData);
                    }
                })
                .finally(() => {
                    isLoadingData.value = false;
                });
        }
    }, [timeRange.value, timeUnit, chartStoreMap]);

    return { chartStoreMap, drawingConfigList, newerExhausted, alertConfigList };
};