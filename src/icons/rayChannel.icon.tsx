export default function RayChannelIcon(props: {
    color?: string
    size?: number
}) {
    const size = props.size ?? 23
    const circleRadius = 1.5
    const color = props.color ?? 'currentColor'

    // Function to offset line endpoints
    function shortenLine(x1: number, y1: number, x2: number, y2: number, r: number) {
        const dx = x2 - x1
        const dy = y2 - y1
        const len = Math.sqrt(dx * dx + dy * dy)
        const ux = dx / len
        const uy = dy / len
        return {
            x1: x1 + ux * r,
            y1: y1 + uy * r,
            x2: x2 - ux * r,
            y2: y2 - uy * r,
        }
    }

    // First ray: (4,6) to (12,8)
    const ray1 = shortenLine(4, 6, 12, 8, circleRadius)
    // First ray extension: (12,8) to (20,10)
    const ray1b = shortenLine(12, 8, 20, 10, circleRadius)

    // Second ray: (4,18) to (12,16)
    const ray2 = shortenLine(4, 18, 12, 16, circleRadius)
    // Second ray extension: (12,16) to (20,14)
    const ray2b = shortenLine(12, 16, 20, 14, circleRadius)

    return (
        <svg
            width={size}
            height={size}
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            {/* First ray */}
            <line x1={ray1.x1} y1={ray1.y1} x2={ray1.x2} y2={ray1.y2} stroke={color} strokeWidth="1" />
            <line x1={ray1b.x1} y1={ray1b.y1} x2={ray1b.x2} y2={ray1b.y2} stroke={color} strokeWidth="1" />

            {/* Second ray */}
            <line x1={ray2.x1} y1={ray2.y1} x2={ray2.x2} y2={ray2.y2} stroke={color} strokeWidth="1" />
            <line x1={ray2b.x1} y1={ray2b.y1} x2={ray2b.x2} y2={ray2b.y2} stroke={color} strokeWidth="1" />

            {/* Anchor points - unfilled */}
            <circle cx="4" cy="6" r={circleRadius} stroke={color} fill="none" strokeWidth="1" />
            <circle cx="12" cy="8" r={circleRadius} stroke={color} fill="none" strokeWidth="1" />
            <circle cx="4" cy="18" r={circleRadius} stroke={color} fill="none" strokeWidth="1" />
            <circle cx="12" cy="16" r={circleRadius} stroke={color} fill="none" strokeWidth="1" />
        </svg>
    )
}
