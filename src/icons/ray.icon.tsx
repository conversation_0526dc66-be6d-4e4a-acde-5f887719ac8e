export default function RayIcon(props: {
    color?: string
    size?: number
}) {
    return (
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width={props.size ?? 23} height={props.size ?? 23}><g fill={props.color ?? 'currentColor'} fillRule="nonzero"><path d="M8.354 20.354l5-5-.707-.707-5 5z"></path><path d="M16.354 12.354l8-8-.707-.707-8 8z"></path><path d="M14.5 15c.828 0 1.5-.672 1.5-1.5s-.672-1.5-1.5-1.5-1.5.672-1.5 1.5.672 1.5 1.5 1.5zm0 1c-1.381 0-2.5-1.119-2.5-2.5s1.119-2.5 2.5-2.5 2.5 1.119 2.5 2.5-1.119 2.5-2.5 2.5zM6.5 23c.828 0 1.5-.672 1.5-1.5s-.672-1.5-1.5-1.5-1.5.672-1.5 1.5.672 1.5 1.5 1.5zm0 1c-1.381 0-2.5-1.119-2.5-2.5s1.119-2.5 2.5-2.5 2.5 1.119 2.5 2.5-1.119 2.5-2.5 2.5z"></path></g></svg>
    )
}