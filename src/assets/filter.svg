 <svg>
    <!-- Main Filter for default state -->
    <filter id="glass-distortion" x="0%" y="0%" width="100%" height="100%" filterUnits="objectBoundingBox"
      color-interpolation-filters="sRGB">
      <!-- STEP 1: Apply a blur to the SourceGraphic (the element itself) -->
      <feGaussianBlur in="SourceGraphic" stdDeviation="8" result="initialBlur" />

      <!-- STEP 2: Generate Perlin noise for distortion.
                 Increased baseFrequency, especially in Y, and more octaves create a more
                 stretched and complex, 'bending light' like noise pattern. -->
      <feTurbulence type="fractalNoise" baseFrequency="0.01 0.01" numOctaves="1" seed="5" result="turbulenceNoise" />

      <!-- STEP 3: Blurs the noise to create smoother transitions for displacement -->
      <feGaussianBlur in="turbulenceNoise" stdDeviation="5" result="blurredNoise" />

      <!-- STEP 4: Distorts the initially blurred graphic based on the R and G channels of blurredNoise.
                 Significantly increased 'scale' for a more intense, twisted, and stretched effect. -->
      <feDisplacementMap in="initialBlur" in2="blurredNoise" scale="150" xChannelSelector="R" yChannelSelector="G" />
    </filter>
  </svg>