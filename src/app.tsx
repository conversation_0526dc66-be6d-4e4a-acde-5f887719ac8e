import { ChartGroup } from './components/chartGroup';
import { DataContextProvider } from './contexts/dataContextProvider';
import { LayoutContextProvider } from './contexts/layoutContext';
import { AppContainer, BlueprintGrid, Footer, globalStyles, Header, SnapshotButton } from './styles/appStyles';

import { useEffect, useRef } from 'react';
import { useSignal } from './hooks/useSignal';
import { XAxis } from './components/xAxis';
import { Selector } from './components/selector';
import { fetchSymbolList, fetchTimeframeList } from './commons/api';
import { disableDebug, } from './commons/util';
import { TRange } from './types/commonTypes';
import { AppState } from './components/appState';
import { Toolbox } from './components/toolbox';
import { Logo } from './components/logo';
import SnapshotIcon from './icons/snapshot.icon';
import { TradingPanel } from './components/tradingPanel';
import { NotificationContextProvider } from './contexts/notificationContext';
import { Countdown } from './components/countdown';
import { AppContextProvider } from './contexts/appContextProvider';
import html2canvas from 'html2canvas';
import { AlertList } from './components/alertList'
disableDebug()


export const App = () => {
  console.debug('rendering app')
  globalStyles();
  const appContainer = useRef<HTMLDivElement>(null);
  const symbolOptions = useSignal<{ label: string, value: string }[]>([])
  const timeframeOptions = useSignal<{ label: string, value: string }[]>([])
  symbolOptions.use()
  timeframeOptions.use()

  const pathParams = getPathParams()

  const symbol = useSignal(pathParams?.symbol ?? localStorage.getItem('symbol') ?? 'btc')
  const timeframe = useSignal(pathParams?.timeframe ?? localStorage.getItem('timeframe') ?? '3m')

  if (symbol.value !== localStorage.getItem('symbol')) localStorage.setItem('symbol', symbol.value)
  if (timeframe.value !== localStorage.getItem('timeframe')) localStorage.setItem('timeframe', timeframe.value)

  // Update URL when symbol or timeframe changes
  const updateUrl = () => {
    const newPath = `/${symbol.value}-${timeframe.value}`;
    if (window.location.pathname !== newPath) {
      window.history.pushState(null, '', newPath);
    }
  };

  useEffect(() => {
    updateUrl();
  }, [symbol.value, timeframe.value]);

  const defaultTimeRange = localStorage.getItem('timeRange') ? JSON.parse(localStorage.getItem('timeRange')!) as TRange : undefined

  symbol.use()
  timeframe.use()


  useEffect(() => {
    const handleNavigation = () => {
      const path = window.location.pathname; // e.g., '/btc-3m'
      const match = path.match(/^\/([a-zA-Z]+)-([a-zA-Z0-9]+)/);
      console.log(match)
      if (match) {
        const [, pathSymbol, pathTimeframe] = match;

        // Update signals if valid values are found in path
        if (symbolOptions.value.some(opt => opt.value === pathSymbol)) {
          symbol.value = pathSymbol;
          localStorage.setItem('symbol', pathSymbol);
        }
        if (timeframeOptions.value.some(opt => opt.value === pathTimeframe)) {
          timeframe.value = pathTimeframe;
          localStorage.setItem('timeframe', pathTimeframe);
        }
      }
    }
    window.addEventListener('popstate', handleNavigation);
    return () => window.removeEventListener('popstate', handleNavigation);
  }, [symbolOptions.value, timeframeOptions.value, symbol, timeframe]);


  useEffect(() => {
    fetchSymbolList().then(data => {
      symbolOptions.value = data.map((item: string) => ({ label: item.toUpperCase(), value: item }))
    })
    fetchTimeframeList().then(data => {
      timeframeOptions.value = data.map((item: string) => ({ label: item, value: item }))
    })
  }, [])


  useEffect(() => {
    const handleKeyPress = (event: KeyboardEvent) => {
      const currentSymbolIndex = symbolOptions.value.findIndex(opt => opt.value === symbol.value);
      const currentTimeframeIndex = timeframeOptions.value.findIndex(opt => opt.value === timeframe.value);

      // Switch symbol with 's' (next) or 'S' (previous), recursively
      if (event.key === 'ArrowDown') {
        const nextIndex = (currentSymbolIndex + 1) % symbolOptions.value.length;
        symbol.value = symbolOptions.value[nextIndex].value;
      } else if (event.key === 'ArrowUp') {
        const prevIndex = (currentSymbolIndex - 1 + symbolOptions.value.length) % symbolOptions.value.length;
        symbol.value = symbolOptions.value[prevIndex].value;
      }

      // Switch timeframe with 't' (next) or 'T' (previous), recursively
      if (event.key === 'ArrowRight') {
        const nextIndex = (currentTimeframeIndex + 1) % timeframeOptions.value.length;
        timeframe.value = timeframeOptions.value[nextIndex].value;
      }
    };

    window.addEventListener('keydown', handleKeyPress);
    return () => window.removeEventListener('keydown', handleKeyPress);
  }, [symbol, timeframe, symbolOptions, timeframeOptions]);

  const capturePage = () => {
    if (!appContainer.current) return;
    html2canvas(appContainer.current).then(canvas => {
      const imgData = canvas.toDataURL('image/png');
      const link = document.createElement('a');
      link.href = imgData;
      link.download = `${symbol.value}-${timeframe.value}-screenshot.png`;
      link.click();
    });
  };


  return (
    <NotificationContextProvider>
      <AppContextProvider
        symbol={symbol.value}
        timeframe={timeframe.value}
        defaultTimeRange={defaultTimeRange}
      >
        <DataContextProvider>
          <AppContainer ref={appContainer}>
            <AppState />
            <BlueprintGrid />
            <LayoutContextProvider container={appContainer}>

              <ChartGroup />
              <Header>
                <Logo />
                <Selector
                  width={100}
                  value={symbol.value}
                  options={symbolOptions.value}
                  onSelectChange={value => {
                    symbol.value = value
                  }}
                />
                <Selector
                  value={timeframe.value}
                  options={timeframeOptions.value}
                  onSelectChange={value => {
                    timeframe.value = value
                  }}
                />
              </Header>
              <Footer>
                <XAxis />
                <Countdown />
              </Footer>
            </LayoutContextProvider>
          </AppContainer>
          <Toolbox />
          <TradingPanel />
          <AlertList />
          <SnapshotButton onClick={capturePage}>
            <SnapshotIcon size={30} />
          </SnapshotButton>
        </DataContextProvider>

      </AppContextProvider>
    </NotificationContextProvider>

  )
}


function getPathParams() {
  const path = window.location.pathname; // e.g., '/btc-3m'
  const match = path.match(/^\/([a-zA-Z]+)-([a-zA-Z0-9]+)/);
  if (!match) return null
  const [, symbol, timeframe] = match;
  return { symbol, timeframe }
}