import { ReactNode, useEffect, useMemo } from "react";
import { ChartStore } from "../store/chartStore";
import { useSignal } from "../hooks/useSignal";
import { TRange, } from "../types/commonTypes";
import { DEFAULT_CHART_PADDING_RATIO, } from "../commons/constants";
import { ChartContext, useAppContext, useDataContext } from "./contexts";
import { TDrawingConfig } from "../types/toolboxTypes";
import { TChartContext } from "../types/contextTypes";

export const ChartContextProvider = (props: { children: ReactNode, chartStore: ChartStore, height: number, paneWidth: number, yAxisWidth: number }) => {
    console.debug('rendering chart context provider')
    const autoScale = useSignal(true)
    const valueRange = useSignal<TRange>({ min: 0, max: 0 })
    const { timeRange } = useAppContext()
    const { drawingConfigList } = useDataContext()
    const chartDrawingConfigList = useSignal<TDrawingConfig[]>([])
    const xRange = useSignal<TRange>({ min: 0, max: props.paneWidth })
    const yRange = useSignal<TRange>({ min: 0, max: props.height })

    timeRange.use()
    autoScale.use()
    drawingConfigList.use()

    useEffect(() => {
        if (!props.chartStore) return
        const timeRange = props.chartStore.getTimeRange()
        if (!timeRange) return
        const chartValueRange = props.chartStore.getValueRangeByTime(timeRange.min, timeRange.max)
        if (chartValueRange) {
            const span = chartValueRange.max - chartValueRange.min
            const padding = span * DEFAULT_CHART_PADDING_RATIO
            valueRange.value = {
                min: chartValueRange.min - padding,
                max: chartValueRange.max + padding
            }
        }
    }, [props.chartStore])

    useEffect(() => {
        chartDrawingConfigList.value = drawingConfigList.value.filter(config => config.chart === props.chartStore.name)
    }, [drawingConfigList.value])

    useEffect(() => {
        const chartValueRange = props.chartStore.getValueRangeByTime(timeRange.value.min, timeRange.value.max)
        if (chartValueRange && autoScale.value) {
            const span = chartValueRange.max - chartValueRange.min
            const padding = span * DEFAULT_CHART_PADDING_RATIO
            valueRange.value = {
                min: chartValueRange.min - padding,
                max: chartValueRange.max + padding
            }
        }
    }, [props.chartStore, timeRange.value, autoScale.value])

    useEffect(() => {
        xRange.value = { min: 0, max: props.paneWidth }
        yRange.value = { min: 0, max: props.height }
    }, [props.paneWidth, props.height])


    const value: TChartContext = useMemo(() => ({
        chartName: props.chartStore.name,
        paneWidth: props.paneWidth,
        height: props.height,
        yAxisWidth: props.yAxisWidth,
        autoScale,
        valueRange,
        chartDrawingConfigList,
        chartStore: props.chartStore,
        xRange,
        yRange
    }), [props.chartStore, autoScale, valueRange, props.paneWidth, props.height, props.yAxisWidth]);

    return (
        <ChartContext.Provider value={value}>
            {props.children}
        </ChartContext.Provider>
    );
}