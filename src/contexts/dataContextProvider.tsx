import { DataContext } from "./contexts";
import { ReactNode } from "react";

import { useChartData } from "../hooks/useChartData";
import { useLiveData } from "../hooks/useLiveData";
import { useSignal } from "../hooks/useSignal";
import { TOrderData } from "../types/tradingTypes";


export const DataContextProvider = (props: { children: ReactNode }) => {
    console.debug('rendering data context provider')
    const { chartStoreMap, drawingConfigList, newerExhausted, alertConfigList } = useChartData()
    const { ticker, positions } = useLiveData(chartStoreMap, newerExhausted)

    // Initialize orders signal with sample data for testing
    const orders = useSignal<TOrderData[]>([
        {
            id: "419",
            symbol: "sol",
            orderId: "0197df8a-2b6f-7000-a2b6-327490f7fbed",
            side: "buy",
            isClose: "1",
            createdAt: Date.now().toString()
        },
        {
            id: "420",
            symbol: "sol",
            orderId: "0197df8a-2b6f-7000-a2b6-327490f7fbee",
            side: "sell",
            isClose: "0",
            createdAt: Date.now().toString()
        }
    ])

    return (
        <DataContext.Provider value={{ chartStoreMap, ticker, drawingConfigList, positions, alertConfigList, orders }}>
            {props.children}
        </DataContext.Provider>
    );
}