import { DataContext } from "./contexts";
import { ReactNode } from "react";

import { useChartData } from "../hooks/useChartData";
import { useLiveData } from "../hooks/useLiveData";


export const DataContextProvider = (props: { children: ReactNode }) => {
    console.debug('rendering data context provider')
    const { chartStoreMap, drawingConfigList, newerExhausted, alertConfigList } = useChartData()
    const { ticker, positions } = useLiveData( chartStoreMap, newerExhausted)

    return (
        <DataContext.Provider value={{ chartStoreMap, ticker, drawingConfigList, positions, alertConfigList }}>
            {props.children}
        </DataContext.Provider>
    );
}