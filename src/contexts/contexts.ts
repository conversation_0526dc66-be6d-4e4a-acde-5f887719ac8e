import { createContext, useContext, } from 'react';

import { TAppContext, TChartContext, TDataContext, TLayoutContext, TNotificationContext, TPaneContext } from '../types/contextTypes';


export const AppContext = createContext<TAppContext | undefined>(undefined);

export const useAppContext = () => {
    const context = useContext(AppContext);
    if (!context) {
        throw new Error('useAppContext must be used within an AppContextProvider');
    }
    return context;
}


export const DataContext = createContext<TDataContext | undefined>(undefined);

export const useDataContext = () => {
    const context = useContext(DataContext);
    if (!context) {
        throw new Error('useDataContext must be used within an DataContextProvider');
    }
    return context;
}



export const LayoutContext = createContext<TLayoutContext | undefined>(undefined);

export const useLayoutContext = () => {
    const context = useContext(LayoutContext);
    if (!context) {
        throw new Error('useLayoutContext must be used within an LayoutContextProvider');
    }
    return context;
}

export const NotificationContext = createContext<TNotificationContext | undefined>(undefined);

export const useNotificationContext = () => {
    const context = useContext(NotificationContext);
    if (!context) {
        throw new Error('useNotificationContext must be used within an NotificationContextProvider');
    }
    return context;
}


export const ChartContext = createContext<TChartContext | undefined>(undefined);

export const useChartContext = () => {
    const context = useContext(ChartContext);
    if (!context) {
        throw new Error('useChartContext must be used within an ChartContextProvider');
    }
    return context;
}

