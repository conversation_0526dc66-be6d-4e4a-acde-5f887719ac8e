import { ReactNode, useEffect, useMemo } from 'react';
import { TNotification, TSuccessNotification, TErrorNotification } from '../types/notificationTypes';
import { savePushSubscription } from '../commons/api';
import { navigateTo } from '../commons/util';
import { TNotificationContext } from '../types/contextTypes';
import { NotificationContext } from './contexts';

const PUBLIC_VAPID_KEY = 'BPb3dU4XzYkcjX7lOyHy4dZ7bRqvuqQGhXXVAdQXvQ933XzyBjS4upA36BSqW7NhTrWLbEITVqUJ2nZyB8odZsw'; // Replace with your VAPID key

export const NotificationContextProvider = (props: { children: ReactNode }) => {
    console.debug('rendering notification context provider')

    const success = (payload: TSuccessNotification) => {
        addNotification({ ...payload, type: 'success', title: '🍀🥑🌈 ' + (payload.title ?? 'Success') })
    }

    const error = (payload: TErrorNotification) => {
        addNotification({ ...payload, type: 'error', title: '🍄💥👀 ' + (payload.title ?? 'Error') })
    }
    const value: TNotificationContext = useMemo(() => ({
        success,
        error,
    }), []);

    useEffect(() => {
        if (Notification.permission !== 'granted') {
            Notification.requestPermission()
                .then(permission => {
                    console.log('Notification permission:', permission);
                })
                .catch(error => {
                    console.error('Error requesting notification permission:', error);
                });
        }


        navigator.serviceWorker.register('/service-worker.js').then(async registration => {
            console.log('Service Worker registered with scope:', registration.scope);
            let subscription = await registration.pushManager.getSubscription();
            if (!subscription) {
                // Create new subscription
                subscription = await registration.pushManager.subscribe({
                    userVisibleOnly: true,
                    applicationServerKey: urlBase64ToUint8Array(PUBLIC_VAPID_KEY)
                });
            }
            if (isApp()) {
                await savePushSubscription(subscription);
            }
        }).catch(error => {
            console.error('Service Worker registration failed:', error);
        });
    }, [])

    return (
        <NotificationContext.Provider value={value}>
            {props.children}
        </NotificationContext.Provider>
    );
}

function addNotification(notification: TNotification) {
    if (Notification.permission !== 'granted') return;
    try {
        const systemNotification = new window.Notification(
            notification.title || 'Solaris Notification',
            {
                body: notification.message,
                icon: notification.icon || '/solaris.png',
                data: {
                    url: notification.url || '/' // Include URL for navigation
                }
            }
        );

        // Auto close the notification after the duration
        if (notification.duration) {
            setTimeout(() => {
                systemNotification.close();
            }, notification.duration);
        }

        // Handle notification click
        systemNotification.onclick = () => {
            console.log(notification)
            // Focus the window
            window.focus();

            // Navigate to the URL if provided
            if (notification.url && window.location.pathname !== notification.url) {
                console.log(notification.url)
                navigateTo(notification.url);
            }

            // Close the notification
            systemNotification.close();
        };
    } catch (error) {
        console.error('Error showing system notification:', error);
    }
}

function urlBase64ToUint8Array(base64String: string) {
    const padding = '='.repeat((4 - base64String.length % 4) % 4);
    const base64 = (base64String + padding)
        .replace(/-/g, '+')
        .replace(/_/g, '/');

    const rawData = window.atob(base64);
    const outputArray = new Uint8Array(rawData.length);

    for (let i = 0; i < rawData.length; ++i) {
        outputArray[i] = rawData.charCodeAt(i);
    }
    return outputArray;
}

function isApp() {
    return window.matchMedia('(display-mode: standalone)').matches
}
