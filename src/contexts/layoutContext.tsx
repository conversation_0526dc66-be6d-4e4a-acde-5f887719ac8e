import { XAXIS_HEIGHT, YAXIS_WIDTH } from "../commons/constants";
import { ReactNode, useEffect, useState } from "react";
import { TLayoutInfo } from "../types/contextTypes";
import { useDataContext, LayoutContext } from "./contexts";


export const LayoutContextProvider = (props: { children: ReactNode, container: React.RefObject<HTMLDivElement | null> }) => {
    console.debug('rendering layout context provider')
    const [layoutInfo, setLayoutInfo] = useState<TLayoutInfo>()
    const { chartStoreMap } = useDataContext()
    const chartCount = chartStoreMap?.size ?? 0


    useEffect(() => {
        if (!props.container.current || !chartCount) return

        const handleResize = () => {
            if (!chartStoreMap) return
            if (!props.container.current) return
            const totalFlex = 4 + (chartCount - 1);
            const chartLayoutInfoMap = new Map()
            const availHeight = props.container.current.clientHeight - XAXIS_HEIGHT;
            const storeList = Array.from(chartStoreMap.values())
            for (let i = 0; i < chartCount; i++) {
                const flex = i === 0 ? 4 : 1;
                const chartHeight = (flex / totalFlex) * availHeight;
                const top = i === 0 ? 0 : ((4 + i - 1) / totalFlex) * availHeight;
                chartLayoutInfoMap.set(storeList[i].name, { height: chartHeight, top })
            }

            setLayoutInfo({
                width: props.container.current.clientWidth,
                height: props.container.current.clientHeight,
                chartGroupHeight: availHeight,
                chartLayoutInfoMap,
                xAxisWidth: props.container.current.clientWidth - YAXIS_WIDTH,
            })
        }

        const obs = new ResizeObserver(handleResize);

        obs.observe(props.container.current);

        return () => {
            if (props.container.current) {
                obs.unobserve(props.container.current);
            }
            obs.disconnect();
        };
    }, [props.container, chartCount]);

    return (
        <LayoutContext.Provider value={{ layoutInfo }}>
            {props.children}
        </LayoutContext.Provider>
    );
}

