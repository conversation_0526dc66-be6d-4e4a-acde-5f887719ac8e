export interface TPosition {
    symbol: string;
    side: string;
    cryptoAmount: string;
    usdAmount: string;
    unrealizedPnl: string;
    pnlPct: string;
}

export interface TMarketOrderResponse {
    orderId: string;
    cryptoAmount: string;
    symbol: string;
    side: string;

}

export interface TClosePositionResponse {
    orderId: string;
    symbol: string;
    side: string;
}

export interface TMarketOrderParams {
    symbol: string;
    side: string;
    usdAmount: number;
}

export enum EPositionSide {
    Long = 'long',
    Short = 'short'
}

export interface TOrderData {
    id: string;
    symbol: string;
    orderId: string;
    side: 'buy' | 'sell';
    isClose: string; // "1" for close, "0" for open
    createdAt: string; // timestamp as string
}

export interface TOrderMarker {
    order: TOrderData;
    x: number; // chart x coordinate
    y: number; // chart y coordinate
    stackIndex: number; // for vertical stacking
}