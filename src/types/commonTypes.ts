export type TRange = {
    min: number;
    max: number;
}

export type TCoord = {
    x: number;
    y: number;
}

export type TTicker = {
    time: number;
    price: number;
}

export type TArea = { x: number; y: number; width: number; height: number };

export type TSignal<T> = { value: T } & { use(): T };


export type MaybePromise<T> = T | Promise<T>;
export type Debounced<T extends (...args: any[]) => any> = (
    ...args: Parameters<T>
) => ReturnType<T> extends Promise<infer R> ? Promise<R> : ReturnType<T>;
