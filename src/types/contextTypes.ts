import { type ChartStore } from "../store/chartStore";
import { TAlertConfig } from "./alertTypes";
import { TSignal, TCoord, TRange, TTicker } from "./commonTypes";
import { TContextMenuState } from "./contextMenuTypes";
import { TErrorNotification, TSuccessNotification } from "./notificationTypes";
import { ETool, TDrawingConfig } from "./toolboxTypes";
import { TPosition } from "./tradingTypes";

export type TAppContext = {
    timeUnit: number;
    symbol: string;
    timeframe: string;
    activeChart: TSignal<string>
    crosshairsCoord: TSignal<TCoord | null>
    activeTool: TSignal<ETool | string>
    timeRange: TSignal<TRange>
    selectedDrawingConfig: TSignal<TDrawingConfig | null>
    contextMenuState: TSignal<TContextMenuState | null>
};

export type TDataContext = {
    chartStoreMap: Map<string, ChartStore> | null,
    ticker: TSignal<TTicker | null>
    drawingConfigList: TSignal<TDrawingConfig[]>
    positions: TSignal<TPosition[]>
    alertConfigList: TSignal<TAlertConfig[]>
}


export type TLayoutInfo = {
    width: number;
    height: number;
    chartGroupHeight: number;
    xAxisWidth: number;
    chartLayoutInfoMap: Map<string, { height: number, top: number }>
}

export type TLayoutContext = {
    layoutInfo?: TLayoutInfo
}


export type TNotificationContext = {
    success: (payload: TSuccessNotification) => void;
    error: (payload: TErrorNotification) => void;
};

export type TChartContext = {
    autoScale: TSignal<boolean>,
    valueRange: TSignal<TRange>
    chartName: string;
    chartDrawingConfigList: TSignal<TDrawingConfig[]>
    chartStore: ChartStore;
    paneWidth: number;
    height: number;
    yAxisWidth: number;
    xRange: TSignal<TRange>
    yRange: TSignal<TRange>
}

export type TPaneContext = {
    contextMenuState: TSignal<TContextMenuState | null>
}   