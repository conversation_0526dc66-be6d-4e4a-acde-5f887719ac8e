import { AXIS_LABEL_COLOR } from "../../commons/constants"
import { useAppContext, useChartContext } from "../../contexts/contexts"
import { YAxisLabel } from "./yAxisLabel"

export const CrosshairsYLabel = () => {
    const { crosshairsCoord, activeChart } = useAppContext()
    const { chartName } = useChartContext()
    crosshairsCoord.use()

    return <YAxisLabel visible={!!crosshairsCoord.value && activeChart.value === chartName} y={crosshairsCoord.value?.y} fontColor="white" backgroundColor={AXIS_LABEL_COLOR} borderColor={AXIS_LABEL_COLOR} />
}