import { Stage, Layer } from "react-konva"
import Konva from "konva"
import { startTransition, useRef } from "react"
import { useAppContext, useChartContext } from "../../contexts/contexts"
import { findClosestDivisible } from "../../commons/util"
import { usePaneScale } from "../../hooks/useScale"
import { Crosshairs } from "../crosshairs"
import { ETool } from "../../types/toolboxTypes"
import { Drawing } from "./drawings/drawing"
import { Plot } from "./plots/plot"
import { ContextMenu } from '../contextMenu'
import { AlertVisualizer } from "./valueAlert"

export const Pane = () => {
    console.debug('rendering pane')
    const {
        chartName,
        chartStore,
        autoScale,
        valueRange,
        paneWidth,
        height,
        chartDrawingConfigList
    } = useChartContext()


    const {
        crosshairsCoord,
        contextMenuState,
        timeRange,
        timeUnit,
        activeChart,
        activeTool,
        symbol,
        timeframe
    } = useAppContext()
    const stageRef = useRef<Konva.Stage>(null)

    const { deltaX2t, deltaY2v, x2t, t2x, y2v } = usePaneScale()


    activeChart.use()
    activeTool.use()
    chartDrawingConfigList.use()

    return (
        <>
            <Stage
                ref={stageRef}
                width={paneWidth}
                height={height}
                draggable
                dragBoundFunc={function () {
                    return {
                        x: this.absolutePosition().x,
                        y: this.absolutePosition().y
                    }
                }}
                onDragMove={onDragMove}
                onWheel={onWheel}
                onMouseMove={onMouseMove}
                onMouseEnter={onMouseEnter}
                onMouseLeave={onMouseLeave}
                onContextMenu={(e) => {
                    e.evt.preventDefault();
                    e.cancelBubble = true
                    if (crosshairsCoord.value) {
                        contextMenuState.value = {
                            coord: crosshairsCoord.value,
                            chartName
                        }
                    }
                }}
                onClick={() => console.log({
                    time: x2t(crosshairsCoord.value!.x),
                    value: y2v(crosshairsCoord.value!.y)
                })}
            >
                <Layer>
                    {
                        chartStore.plotStoreList.map(plotStore => {
                            return <Plot store={plotStore} key={plotStore.id} />
                        })
                    }
                </Layer>
                <Layer>
                    <AlertVisualizer />
                    {
                        activeChart.value === chartName && activeTool.value !== ETool.None && (
                            <Drawing key={activeTool.value} config={{ symbol, timeframe, chart: chartName, type: activeTool.value, dataPoints: [] }} preview />
                        )
                    }
                    {
                        chartDrawingConfigList.value.map(config => {
                            return <Drawing key={config.id} config={config} />
                        })
                    }

                </Layer>
                <Layer>
                    <Crosshairs />
                </Layer>
            </Stage>
            <ContextMenu />
        </>
    )

    function onMouseLeave(e: Konva.KonvaEventObject<MouseEvent>) {
        activeChart.value = ''
    }

    function onMouseEnter(e: Konva.KonvaEventObject<MouseEvent>) {
        activeChart.value = chartStore.name
    }

    function onMouseMove(e: Konva.KonvaEventObject<MouseEvent>) {
        const stage = e.target.getStage() as Konva.Stage
        const mousePos = stage.getPointerPosition() as Konva.Vector2d

        crosshairsCoord.value = {
            x: t2x(findClosestDivisible(x2t(mousePos.x), timeUnit)),
            y: mousePos.y
        }
    }

    function onWheel(e: Konva.KonvaEventObject<WheelEvent>) {
        e.cancelBubble = true
        const wheelDelta = e.evt.deltaY;
        const wheelDeltaX = e.evt.deltaX;

        // Ignore horizontal scroll attempts
        if (Math.abs(wheelDeltaX) > Math.abs(wheelDelta)) return;

        const zoomFactor = Math.pow(0.999, -wheelDelta);

        const prev = timeRange.value
        const timeSpan = prev.max - prev.min
        const newTimeSpan = timeSpan * zoomFactor
        const centerTime = prev.min + (timeSpan / 2)
        const newMin = centerTime - (newTimeSpan / 2)
        const newMax = centerTime + (newTimeSpan / 2)

        // Check if the new range would exceed the 2000 bar limit
        const barsInView = (newMax - newMin) / timeUnit;
        if (barsInView > 2000 && zoomFactor > 1) {
            // If zooming out would exceed limit, maintain current range
            return
        }
        timeRange.value = {
            min: newMin,
            max: newMax
        }
    }

    function onDragMove(e: Konva.KonvaEventObject<DragEvent>) {
        e.cancelBubble = true
        const stage = e.target.getStage() as Konva.Stage
        const mousePos = stage.getPointerPosition() as Konva.Vector2d
        crosshairsCoord.value = {
            x: t2x(findClosestDivisible(x2t(mousePos.x), timeUnit)),
            y: mousePos.y
        }

        if (e.target !== stageRef.current) return
        const deltaX = e.evt.movementX
        const deltaY = e.evt.movementY
        if (deltaX !== 0) {
            const timeDelta = deltaX2t(deltaX)
            startTransition(() => {
                timeRange.value = {
                    min: timeRange.value.min - timeDelta,
                    max: timeRange.value.max - timeDelta
                }
            })
        }


        if (!autoScale.value && deltaY !== 0) {
            const valueDelta = deltaY2v(deltaY)
            valueRange.value = {
                min: valueRange.value.min + valueDelta,
                max: valueRange.value.max + valueDelta
            }
        }
    }
}