import { ChartStore } from "../../store/chartStore"
import { ChartContainer } from "../../styles/chartStyles"
import { ChartContextProvider } from "../../contexts/chartContextProvider"

import { YAxis } from "./yAxis"
import { YAXIS_WIDTH } from "../../commons/constants"
import { Pane } from "./pane"

export const Chart = (props: { style?: React.CSSProperties, store: ChartStore, width: number, height: number }) => {

    const paneWidth = props.width
    const yAxisWidth = YAXIS_WIDTH
    const height = props.height

    return (
        <ChartContainer style={props.style}>
            <ChartContextProvider chartStore={props.store} paneWidth={paneWidth} height={height} yAxisWidth={yAxisWidth}>
                <Pane />
                <YAxis />
            </ChartContextProvider>
        </ChartContainer>
    )
}