import { Label, Tag, Text } from "react-konva";
import { useEffect, useRef, useState } from "react";
import Konva from "konva";
import { useYAxisScale } from "../../hooks/useScale";
import { getFormatNumStr } from "../../commons/util";

interface YAxisLabelProps {
    visible?: boolean;
    value?: number;
    y?: number;
    fontColor?: string;
    backgroundColor?: string;
    borderColor?: string;
    suffix?: string;
}

export const YAxisLabel = ({
    visible = true,
    value,
    y,
    fontColor = "white",
    backgroundColor = "black",
    borderColor = "white",
    suffix = "",
}: YAxisLabelProps) => {
    const { v2y, y2v } = useYAxisScale();
    const labelRef = useRef<Konva.Label>(null);
    const [labelY, setLabelY] = useState<number>(NaN);
    const [labelText, setLabelText] = useState("");
    const [isBlinking, setIsBlinking] = useState(false);
    const [prevValue, setPrevValue] = useState<number | undefined>(undefined);
    const blinkTimeoutRef = useRef<number | null>(null);

    useEffect(() => {
        const labelHeight = labelRef.current?.getText()?.height() ?? 0;
        const calculatedY = y ?? v2y(value ?? 0);
        setLabelY(calculatedY - labelHeight / 2);
        setLabelText(getFormatNumStr(value ?? y2v(calculatedY)));
    }, [labelRef.current, value, y, v2y, y2v]);

    useEffect(() => {
        if (prevValue !== undefined && value !== undefined && value !== prevValue) {
            if (blinkTimeoutRef.current) {
                clearTimeout(blinkTimeoutRef.current);
            }

            setIsBlinking(true);
            blinkTimeoutRef.current = setTimeout(() => setIsBlinking(false), 400);
        }

        setPrevValue(value);

        return () => {
            if (blinkTimeoutRef.current) {
                clearTimeout(blinkTimeoutRef.current);
            }
        };
    }, [value]);

    const colors = isBlinking
        ? { backgroundColor: fontColor, fontColor: backgroundColor, borderColor }
        : { backgroundColor, fontColor, borderColor };

    return (
        !isNaN(labelY) && (
            <Label visible={visible} x={0} y={labelY} ref={labelRef} perfectDrawEnabled={false}>
                <Tag
                    fill={colors.backgroundColor}
                    cornerRadius={12}
                    shadowColor="rgba(0, 0, 0, 0.13)"
                    shadowBlur={5}
                    shadowOffsetY={1}
                />
                <Text
                    align="center"
                    text={`${labelText}${suffix ? `\n\n${suffix}` : ""}`}
                    fontSize={12}
                    fontStyle="bold"
                    padding={8}
                    fill={colors.fontColor}
                    fontFamily="Zed, sans-serif"
                />
            </Label>
        )
    );
};