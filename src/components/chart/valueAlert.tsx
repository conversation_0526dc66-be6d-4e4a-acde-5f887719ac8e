import { useMemo } from 'react';
import { Group, Line, Text } from 'react-konva';
import { useChartContext, useDataContext } from '../../contexts/contexts';
import { usePaneScale } from '../../hooks/useScale';
import { ALERT_COLOR, FONT_FAMILY, YAXIS_WIDTH } from '../../commons/constants';
export const AlertVisualizer = () => {
    const { alertConfigList } = useDataContext()
    const { paneWidth, chartName } = useChartContext()
    const { v2y } = usePaneScale();
    alertConfigList.use()

    // Filter price alerts for the current chart
    const valueAlerts = useMemo(() => {
        if (!chartName) return [];
        return alertConfigList.value.filter(alert =>
            alert.chart === chartName &&
            alert.arg2?.type === 'value'
        );
    }, [alertConfigList.value, chartName]);

    // Render price alerts as horizontal dotted lines
    const renderPriceAlerts = () => {
        return valueAlerts.map(alert => {
            const price = alert.arg2?.value;
            if (price === undefined) return null;

            const y = v2y(price);
            return (
                <Group>
                    <Line
                        key={`price-alert-${alert.id}`}
                        points={[0, y, paneWidth, y]}
                        stroke={ALERT_COLOR}
                        strokeWidth={1.5}
                        dash={[5, 5]} // Dotted line
                    />
                    <Text
                        x={paneWidth - YAXIS_WIDTH - 12 * 6}
                        y={y - 17}
                        text={'Alert #' + alert.id.toString().substring(0, 6)}
                        fontFamily={FONT_FAMILY}
                        fill={ALERT_COLOR}
                        fontSize={12}
                        fontStyle="bold"
                    />
                </Group>

            );
        });
    };

    if (!chartName || valueAlerts.length === 0) {
        return null;
    }

    return (
        <Group>
            {renderPriceAlerts()}
        </Group>
    );
};
