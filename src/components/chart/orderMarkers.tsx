import { useMemo } from 'react';
import { Group, Circle, Text } from 'react-konva';
import { useAppContext, useChartContext, useDataContext } from '../../contexts/contexts';
import { usePaneScale } from '../../hooks/useScale';
import { TOrderData, TOrderMarker } from '../../types/tradingTypes';
import { CandlestickStore } from '../../store/candlestickStore';
import { FONT_FAMILY } from '../../commons/constants';
import { findClosestDivisible } from '../../commons/util';

export const OrderMarkers = () => {
    console.debug('rendering OrderMarkers');

    const { timeRange, timeUnit } = useAppContext();
    const { chartStore, chartName } = useChartContext();
    const { ticker, orders } = useDataContext();
    const { v2y, t2x, deltaT2x } = usePaneScale();

    // Use reactive signals
    timeRange.use();
    ticker.use();
    orders.use();

    // Get the main candlestick store
    const mainPlotStore = chartStore.mainPlotStore as CandlestickStore;

    const orderMarkers = useMemo(() => {
        if (!orders.value.length || !mainPlotStore || chartName !== 'main') {
            return [];
        }

        // Filter orders within the visible time range
        const visibleOrders = orders.value.filter(order => {
            const orderTime = parseInt(order.createdAt);
            return orderTime >= timeRange.value.min && orderTime <= timeRange.value.max;
        });

        if (!visibleOrders.length) {
            return [];
        }

        // Get candlestick data for the visible time range
        const candlesticks = mainPlotStore.selectByTime(timeRange.value.min, timeRange.value.max);

        // Group orders by candlestick timespan
        const orderGroups = new Map<number, TOrderData[]>();

        visibleOrders.forEach(order => {
            const orderTime = parseInt(order.createdAt);

            // Find the closest candlestick time using findClosestDivisible
            const candleTime = findClosestDivisible(orderTime, timeUnit, 'lower');

            // Check if this candlestick exists in our data
            const candlestick = candlesticks.find(c => c.time === candleTime);

            if (candlestick) {
                if (!orderGroups.has(candleTime)) {
                    orderGroups.set(candleTime, []);
                }
                orderGroups.get(candleTime)!.push(order);
            }
        });

        // Create markers with stacking
        const markers: TOrderMarker[] = [];

        orderGroups.forEach((groupOrders, candleTime) => {
            // Sort orders by creation time
            groupOrders.sort((a, b) => parseInt(a.createdAt) - parseInt(b.createdAt));

            const candlestick = candlesticks.find(c => c.time === candleTime);
            if (!candlestick) return;

            const x = t2x(candleTime);
            const candleHigh = v2y(candlestick.high);
            const candleLow = v2y(candlestick.low);

            // Separate buy and sell orders
            const buyOrders = groupOrders.filter(order => order.side === 'buy');
            const sellOrders = groupOrders.filter(order => order.side === 'sell');

            // Position buy orders below the candlestick
            buyOrders.forEach((order, index) => {
                const stackOffset = (index + 1) * 25; // 25px spacing between stacked orders
                markers.push({
                    order,
                    x,
                    y: candleLow + stackOffset,
                    stackIndex: index
                });
            });

            // Position sell orders above the candlestick
            sellOrders.forEach((order, index) => {
                const stackOffset = (index + 1) * 25; // 25px spacing between stacked orders
                markers.push({
                    order,
                    x,
                    y: candleHigh - stackOffset,
                    stackIndex: index
                });
            });
        });

        return markers;
    }, [orders.value, mainPlotStore, timeRange.value, timeUnit, chartName, deltaT2x, t2x, v2y]);

    return (
        <Group>
            {orderMarkers.map((marker, index) => (
                <OrderMarker key={`${marker.order.id}-${index}`} marker={marker} />
            ))}
        </Group>
    );
};

interface OrderMarkerProps {
    marker: TOrderMarker;
}

const OrderMarker = ({ marker }: OrderMarkerProps) => {
    const { order, x, y } = marker;
    const isBuy = order.side === 'buy';
    const isClose = order.isClose === '1';

    // Colors for buy/sell and open/close
    const buyColor = '#26a69a';
    const sellColor = '#ef5350';
    const color = isBuy ? buyColor : sellColor;

    // Different shapes for open/close
    const radius = 8;
    const fontSize = 12;

    return (
        <Group x={x} y={y}>
            {/* Order marker circle */}
            <Circle
                x={0}
                y={0}
                radius={radius}
                fill={color}
                stroke="white"
                strokeWidth={2}
                shadowColor="rgba(0,0,0,0.3)"
                shadowBlur={4}
                shadowOffset={{ x: 1, y: 1 }}
            />

            {/* Order type indicator */}
            <Text
                x={-radius / 2}
                y={-fontSize / 2}
                text={isBuy ? 'B' : 'S'}
                fontSize={fontSize}
                fontFamily={FONT_FAMILY}
                fill="white"
                fontStyle="bold"
                align="center"
                width={radius}
            />

            {/* Close indicator if it's a close order */}
            {isClose && (
                <Circle
                    x={radius - 3}
                    y={-radius + 3}
                    radius={3}
                    fill="white"
                    stroke={color}
                    strokeWidth={1}
                />
            )}
        </Group>
    );
};

// Helper function to find the candlestick that contains the order timestamp
function findCandlestickForOrder(candlesticks: any[], orderTime: number, timeUnit: number): any | null {
    // Find the candlestick whose time period contains the order time
    for (const candlestick of candlesticks) {
        const candleStart = candlestick.time;
        const candleEnd = candlestick.time + timeUnit;

        if (orderTime >= candleStart && orderTime < candleEnd) {
            return candlestick;
        }
    }

    // If no exact match, find the closest candlestick
    let closest = null;
    let minDistance = Infinity;

    for (const candlestick of candlesticks) {
        const distance = Math.abs(candlestick.time - orderTime);
        if (distance < minDistance) {
            minDistance = distance;
            closest = candlestick;
        }
    }

    return closest;
}
