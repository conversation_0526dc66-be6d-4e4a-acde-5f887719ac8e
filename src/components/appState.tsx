import { useEffect } from "react"
import { useAppContext } from "../contexts/contexts"

export const AppState = () => {
    console.debug('rendering app state')
    const { timeRange, symbol, timeframe } = useAppContext()
    timeRange.use()
    useEffect(() => {
        localStorage.setItem('timeRange', JSON.stringify(timeRange.value))
        localStorage.setItem('symbol', symbol)
        localStorage.setItem('timeframe', timeframe)
    }, [timeRange.value, symbol, timeframe])
    return null
}