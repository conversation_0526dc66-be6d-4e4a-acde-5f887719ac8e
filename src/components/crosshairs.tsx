import { Shape } from "react-konva";
import Kon<PERSON> from "konva";
import { useAppContext, useChartContext } from "../contexts/contexts";
import { CROSSHAIR_COLOR } from "../commons/constants";

export const Crosshairs = () => {
    const { activeChart, crosshairsCoord } = useAppContext();
    const { height, paneWidth, chartStore } = useChartContext();
    crosshairsCoord.use();

    const sceneFunc = (context: Konva.Context) => {
        if (!crosshairsCoord.value) return;
        const { x, y } = crosshairsCoord.value;

        context.save();               // keep canvas state tidy
        context.setLineDash([5, 5]);  // <‑‑ THE correct call
        context.strokeStyle = CROSSHAIR_COLOR;
        context.lineWidth = 1.5;

        // horizontal (only on active chart)
        if (activeChart.value === chartStore.name && activeChart.value !== '') {
            context.beginPath();
            context.moveTo(0, y);
            context.lineTo(paneWidth, y);
            context.stroke();
        }

        // vertical (always)
        context.beginPath();
        context.moveTo(x, 0);
        context.lineTo(x, height);
        context.stroke();

        context.restore();
    };


    return crosshairsCoord.value ? (
        <Shape sceneFunc={sceneFunc} listening={false} perfectDrawEnabled={false} />
    ) : null;
};