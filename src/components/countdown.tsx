import React, { useEffect, useState, useRef, useMemo } from "react";
import { useAppContext, useDataContext, useNotificationContext } from "../contexts/contexts";

import { CountdownContainer } from "../styles/appStyles";

import { format } from "date-fns";


interface StoredNotification {
    id: string;
    deadline: number;
    symbol: string;
    timeframe: string;
    notificationSent: boolean;
}

const NOTIFICATION_STORAGE_KEY = 'countdown_notifications';
const NOTIFICATION_DEBOUNCE_TIME = 5000;

const generateNotificationId = () => Math.random().toString(36).substring(2, 15);

const formatTime = (ms: number) => {
    const totalSeconds = Math.floor(ms / 1000);
    const minutes = Math.floor(totalSeconds / 60);
    const seconds = totalSeconds % 60;
    return `${minutes}:${seconds < 10 ? '0' : ''}${seconds}`;
};

export const Countdown = () => {
    const { success, error } = useNotificationContext()
    const { symbol, timeframe, timeUnit } = useAppContext()
    const { ticker } = useDataContext()
    ticker.use()
    const [timeLeft, setTimeLeft] = useState(0);
    const [isNotificationScheduled, setIsNotificationScheduled] = useState(false);
    const nextDeadlineRef = useRef<number | null>(null);
    const lastNotificationTimeRef = useRef<number>(0);
    const intervalRef = useRef<number | null>(null);

    // Memoize context values to prevent re-renders on context changes
    const memoizedContext = useMemo(() => ({ symbol, timeframe }), [symbol, timeframe]);
    const formattedSymbol = memoizedContext.symbol.replace('usdt', '').toUpperCase();


    const startTime = useMemo(() => ticker.value?.time, [ticker.value]);
    const deadline = useMemo(() => startTime ? startTime + timeUnit : null, [startTime, timeUnit]);


    const getStoredNotifications = (): StoredNotification[] => {
        try {
            return JSON.parse(localStorage.getItem(NOTIFICATION_STORAGE_KEY) || '[]');
        } catch {
            return [];
        }
    };

    const saveNotification = (deadlineTime: number) => {
        const id = generateNotificationId();
        const notifications = getStoredNotifications();
        const newNotification: StoredNotification = {
            id,
            deadline: deadlineTime,
            symbol: memoizedContext.symbol,
            timeframe: memoizedContext.timeframe,
            notificationSent: false,
        };

        const filteredNotifications = notifications.filter(
            n => !(n.symbol === memoizedContext.symbol && n.timeframe === memoizedContext.timeframe && !n.notificationSent)
        );
        filteredNotifications.push(newNotification);
        localStorage.setItem(NOTIFICATION_STORAGE_KEY, JSON.stringify(filteredNotifications));
        return id;
    };

    const removeNotification = (id: string) => {
        const notifications = getStoredNotifications().filter(n => n.id !== id);
        localStorage.setItem(NOTIFICATION_STORAGE_KEY, JSON.stringify(notifications));
    };

    const markNotificationSent = (id: string) => {
        const notifications = getStoredNotifications().map(n =>
            n.id === id ? { ...n, notificationSent: true } : n
        );
        localStorage.setItem(NOTIFICATION_STORAGE_KEY, JSON.stringify(notifications));
    };

    const calculateTimeLeft = () => {
        if (!startTime || !deadline) return 0;
        const now = Date.now();
        let nextDeadline = deadline;

        if (now >= deadline) {
            const interval = deadline - startTime;
            const elapsedIntervals = Math.floor((now - startTime) / interval);
            nextDeadline = startTime + (elapsedIntervals + 1) * interval;
        }

        nextDeadlineRef.current = nextDeadline;
        return nextDeadline - now;
    };

    const checkNotifications = () => {
        const now = Date.now();
        // Early debounce check
        if (now - lastNotificationTimeRef.current < NOTIFICATION_DEBOUNCE_TIME) return;

        const notifications = getStoredNotifications();
        const currentNotification = notifications.find(
            n => n.symbol === memoizedContext.symbol && n.timeframe === memoizedContext.timeframe && !n.notificationSent && n.deadline > now
        );

        setIsNotificationScheduled(!!currentNotification);

        notifications.forEach(n => {
            const timeUntilDeadline = n.deadline - now;
            // Tighten the window to 0 to 1000 ms to avoid repeated triggers
            if (timeUntilDeadline > 0 && timeUntilDeadline <= 1000) {
                lastNotificationTimeRef.current = now;
                const formattedSymbol = n.symbol.replace('usdt', '').toUpperCase();
                console.log(n)
                success({
                    message: `Countdown complete for ${formattedSymbol}/${n.timeframe}!`,
                    duration: 10000,
                    url: `/${n.symbol}-${n.timeframe}`,
                });
                markNotificationSent(n.id); // Mark as sent immediately
                if (n.symbol === memoizedContext.symbol && n.timeframe === memoizedContext.timeframe) {
                    setIsNotificationScheduled(false);
                }
            } else if (timeUntilDeadline < -10000) {
                removeNotification(n.id);
            }
        });
    };
    useEffect(() => {
        if (!startTime || !deadline) return;

        // Single interval for time updates and notification checks
        const update = () => {
            const newTimeLeft = calculateTimeLeft();
            // Only update state if the displayed time changes (every second)
            const formattedNew = formatTime(newTimeLeft);
            const formattedOld = formatTime(timeLeft);
            if (formattedNew !== formattedOld) {
                setTimeLeft(newTimeLeft);
            }
            checkNotifications();
        };

        update(); // Initial call
        intervalRef.current = setInterval(update, 1000);

        return () => {
            if (intervalRef.current) clearInterval(intervalRef.current);
        };
    }, [
        startTime,
        deadline,
        timeLeft,
        memoizedContext,
    ]);

    const handleClick = async () => {
        if (!nextDeadlineRef.current) return;
        const now = Date.now();
        const timeUntilDeadline = nextDeadlineRef.current - now;

        if (isNotificationScheduled) {
            const notifications = getStoredNotifications();
            const currentNotification = notifications.find(
                n => n.symbol === memoizedContext.symbol && n.timeframe === memoizedContext.timeframe && !n.notificationSent
            );
            if (currentNotification) removeNotification(currentNotification.id);
            setIsNotificationScheduled(false);
            success({
                message: `${formattedSymbol}/${memoizedContext.timeframe} countdown notification canceled`,
                duration: 2000,
            });
            return;
        }

        if (timeUntilDeadline <= 0) {
            error({
                message: `Cannot schedule notification - deadline passed for ${formattedSymbol}/${memoizedContext.timeframe}`,
                duration: 3000,
            });
            return;
        }

        saveNotification(nextDeadlineRef.current);
        setIsNotificationScheduled(true);
        success({
            message: `${formattedSymbol}/${memoizedContext.timeframe} notification scheduled for ${format(deadline!, 'HH:mm')}`,
            duration: 2000,
        });
    };

    if (!startTime || !deadline) return null;

    return (

        <CountdownContainer
            onClick={handleClick}
            style={{
                color: isNotificationScheduled ? 'rgb(207, 81, 7)' : 'rgb(7, 81, 207)',
            }}
            title={
                isNotificationScheduled
                    ? `Click to cancel ${formattedSymbol}/${memoizedContext.timeframe} notification`
                    : `Click to schedule ${formattedSymbol}/${memoizedContext.timeframe} notification`
            }
        >
            {formatTime(timeLeft)}
        </CountdownContainer>
    );
};

