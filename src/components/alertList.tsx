import { useState } from "react";
import { useAppContext, useDataContext, useNotificationContext } from "../contexts/contexts";
import {
    AlertListContainer,
    AlertListEmpty,
    AlertListItem
} from "../styles/alertStyles";
import { TAlertConfig } from "../types/alertTypes";
import { deleteAlertConfig } from "../commons/api";
import { AlertDescription } from "./alertDescription.tsx";


export const AlertList = () => {
    const [crossedAlerts, setCrossedAlerts] = useState<string[]>([]);
    const { alertConfigList } = useDataContext()
    const { symbol, timeframe } = useAppContext()
    const { success, error } = useNotificationContext()
    alertConfigList.use()

    // Handle single-click on alert item for navigation
    const handleSingleClick = (alert: TAlertConfig) => {
        // Navigate to the alert's symbol and timeframe
        if (alert.symbol && alert.timeframe) {
            const newPath = `/${alert.symbol}-${alert.timeframe}`;
            window.history.pushState(null, '', newPath);

            // Trigger popstate event to update the app state
            window.dispatchEvent(new PopStateEvent('popstate'));
        }
    };

    // Handle double-click on alert item
    const handleDoubleClick = (alert: TAlertConfig) => {
        // Add to crossed list first for visual feedback
        setCrossedAlerts((prev) => [...prev, alert.id]);

        // After a short delay for the visual effect, delete the alert
        setTimeout(() => {
            // Show notification
            success({
                title: 'Alert deleted',
                message: `Alert deleted for ${alert.symbol?.replace('usdt', '').toUpperCase()}/${alert.timeframe}`
            });

            // Delete the alert and keep it crossed out until deletion is complete
            deleteAlertConfig(alert.id)
                .then(() => {
                    alertConfigList.value = alertConfigList.value.filter((a) => a.id !== alert.id);
                    setCrossedAlerts((prev) => prev.filter((id) => id !== alert.id));
                    success({
                        title: 'Alert deleted',
                        message: `Alert deleted for ${alert.symbol?.replace('usdt', '').toUpperCase()}/${alert.timeframe}`
                    })
                })
                .catch((err) => {
                    // If deletion fails, remove from crossed list
                    setCrossedAlerts((prev) => prev.filter((id) => id !== alert.id));

                    error({
                        title: 'Failed to delete alert',
                        message: err.message
                    })
                });
        }, 500);
    };

    // Check if alert is for current symbol and timeframe
    const isCurrentSymbolAndTimeframe = (alert: TAlertConfig) => {
        return alert.symbol === symbol && alert.timeframe === timeframe;
    };

    return (
        <AlertListContainer>
            {alertConfigList.value.length === 0 ? (
                <AlertListEmpty><span style={{ direction: 'ltr', display: 'inline-block' }}>No alerts yet</span></AlertListEmpty>
            ) : (
                alertConfigList.value.map((alert) => (
                    <AlertListItem
                        key={alert.id}
                        crossed={crossedAlerts.includes(alert.id)}
                        current={isCurrentSymbolAndTimeframe(alert)}
                        onClick={() => handleSingleClick(alert)}
                        onDoubleClick={() => handleDoubleClick(alert)}
                    >
                        <span style={{ direction: 'ltr', display: 'inline-block' }}>
                            <AlertDescription alert={alert} />
                        </span>
                    </AlertListItem>
                ))
            )}
        </AlertListContainer>
    );
};
