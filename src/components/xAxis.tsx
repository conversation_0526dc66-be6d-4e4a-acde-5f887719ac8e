import { Layer, Stage } from 'react-konva'
import { XAXIS_HEIGHT } from '../commons/constants'
import { useLayoutContext } from '../contexts/contexts'
import Konva from 'konva'
import { useAppContext } from '../contexts/contexts'
import XAxisLabel from './xAxisLabel'

export const XAxis = () => {
    console.debug('rendering x axis')
    const { layoutInfo } = useLayoutContext()
    const { crosshairsCoord, activeChart } = useAppContext()
    crosshairsCoord.use()
    return (
        layoutInfo &&
        <Stage
            width={layoutInfo.xAxisWidth}
            height={XAXIS_HEIGHT}
            onMouseEnter={onMouseEnter}
        >
            <Layer>
                <XAxisLabel
                    visible={!!crosshairsCoord.value}
                    x={crosshairsCoord.value?.x}
                    y={1}
                />
            </Layer>
        </Stage>
    )


    function onMouseEnter(e: Konva.KonvaEventObject<MouseEvent>) {
        crosshairsCoord.value = null
        activeChart.value = ''
    }
}
