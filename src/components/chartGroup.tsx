
import { useLayoutContext, useDataContext, useAppContext } from "../contexts/contexts";
import { ChartGroupContainer } from "../styles/chartStyles";
import { Chart } from "./chart/chart";


export const ChartGroup = () => {
    console.debug('rendering chart group')
    const { layoutInfo } = useLayoutContext()
    const { chartStoreMap } = useDataContext()
    const { crosshairsCoord } = useAppContext()

    return (
        layoutInfo && chartStoreMap &&
        <ChartGroupContainer onMouseLeave={() => crosshairsCoord.value = null}>
            {Array.from(chartStoreMap.values()).map(chartStore => {
                const chartLayoutInfo = layoutInfo?.chartLayoutInfoMap.get(chartStore.name)
                if (!chartLayoutInfo) return
                return (
                    <Chart
                        store={chartStore}
                        key={chartStore.name}
                        width={layoutInfo.width}
                        height={chartLayoutInfo.height}
                        style={{
                            position: 'absolute',
                            height: chartLayoutInfo.height,
                            top: chartLayoutInfo.top,
                        }}
                    />
                )
            })}
        </ChartGroupContainer>
    );
}