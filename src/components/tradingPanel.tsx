import React, { useState, useCallback, useEffect, useRef } from 'react';
import {
    TradingPanelMainContainer,

    TradingPanelContainer,
    TradingPanelHeader,
    InputContainer,
    AmountInput,
    ActionButton,
    PositionsContainer,
    PositionsList,
    PositionItem,
    PositionDetails,
    PositionSymbol,
    PositionAmount,
    PositionSide,
    PositionPnl,
    FloatingButtonsContainer,
    ButtonsContainer
} from '../styles/tradingStyles';
import { EPositionSide, } from '../types/tradingTypes';
import { useAppContext, useDataContext, useNotificationContext } from '../contexts/contexts';
import { closePosition, fetchAccount, placeMarketOrder } from '../commons/api';
import { useSignal } from '../hooks/useSignal';
import { navigateTo } from '../commons/util';

// interface TradingPanelProps {
//     positions: TPosition[];
// }

function formatSymbol(symbolStr: string) {
    return symbolStr.replace(/usdt$/i, '').toUpperCase()
}

export const TradingPanel = () => {
    const { symbol, timeframe } = useAppContext()
    const { positions } = useDataContext()
    const { success, error } = useNotificationContext()
    const [amount, setAmount] = useState<string>('');
    const [leverage, setLeverage] = useState<number>(20);
    positions.use()
    const isOpen = useSignal(positions.value.length > 0);
    isOpen.use()

    useEffect(() => {
        isOpen.value = positions.value.length > 0
    }, [positions.value]);

    const [buyLoading, setBuyLoading] = useState<boolean>(false);
    const [reverseLoading, setReverseLoading] = useState<boolean>(false);
    const [sellLoading, setSellLoading] = useState<boolean>(false);
    const [closePositionLoading, setClosePositionLoading] = useState<Record<string, boolean>>({});
    const [availableMargin, setAvailableMargin] = useState<string>('');

    const [isDragging, setIsDragging] = useState(false);
    const [dragOffset, setDragOffset] = useState({ x: 0, y: 0 });

    // // Active/Idle state
    const [isActive, setIsActive] = useState(false);
    const [idleTimer, setIdleTimer] = useState<number | null>(null);
    const [isHovering, setIsHovering] = useState(false);

    const panelRef = useRef<HTMLDivElement>(null);

    const defaultPosition = localStorage.getItem('tradingPanelCoord') ? JSON.parse(localStorage.getItem('tradingPanelCoord')!) : { x: 200, y: 100 };

    // // Function to refresh account data (positions and available margin)
    const reloadAccountData = useCallback(async () => {
        try {
            const accountData = await fetchAccount();

            setAvailableMargin(accountData.availableMargin);
            setLeverage(accountData.leverage);

            // // Show drawer if there are positions, hide if empty
            // setShowPositions(accountData.positions.length > 0);

            positions.value = accountData.positions;
        } catch (error: any) {
            console.error('Failed to fetch account data:', error);
            return [];
        }
    }, []);

    // Load positions on initial render
    useEffect(() => {
        reloadAccountData();
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    // // Drag handlers
    const handleMouseDown = useCallback((e: React.MouseEvent) => {
        // Activate the panel

        if (panelRef.current) {
            setIsDragging(true);

            // Calculate the offset between mouse position and panel position
            const rect = panelRef.current.getBoundingClientRect();
            setDragOffset({
                x: e.clientX - rect.left,
                y: e.clientY - rect.top
            });
        }
    }, []);



    const handleMouseMove = useCallback((e: MouseEvent) => {

        if (isDragging && panelRef.current) {
            // Get panel dimensions
            const panelRect = panelRef.current.getBoundingClientRect();
            const panelWidth = panelRect.width;
            const panelHeight = panelRect.height;

            // Calculate new position based on mouse position and offset
            let newX = e.clientX - dragOffset.x;
            let newY = e.clientY - dragOffset.y;

            // Keep panel within viewport bounds
            const viewportWidth = window.innerWidth;
            const viewportHeight = window.innerHeight;

            // Ensure panel stays within horizontal bounds
            if (newX < 0) newX = 0;
            if (newX + panelWidth > viewportWidth) newX = viewportWidth - panelWidth;

            // Ensure panel stays within vertical bounds
            if (newY < 0) newY = 0;
            if (newY + panelHeight > viewportHeight) newY = viewportHeight - panelHeight;

            const newCoord = { x: newX, y: newY };
            panelRef.current.style.top = `${newY}px`;
            panelRef.current.style.left = `${newX}px`;

            // Save position to localStorage
            localStorage.setItem('tradingPanelCoord', JSON.stringify(newCoord));
        }
    }, [isDragging, dragOffset]);

    const handleMouseUp = useCallback(() => {
        setIsDragging(false);
    }, []);

    // Add and remove event listeners for dragging
    useEffect(() => {
        if (isDragging) {
            window.addEventListener('mousemove', handleMouseMove);
            window.addEventListener('mouseup', handleMouseUp);
        } else {
            window.removeEventListener('mousemove', handleMouseMove);
            window.removeEventListener('mouseup', handleMouseUp);
        }

        return () => {
            window.removeEventListener('mousemove', handleMouseMove);
            window.removeEventListener('mouseup', handleMouseUp);
        };
    }, [isDragging, handleMouseMove, handleMouseUp]);


    const handleAmountChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        // Only allow numbers and decimal point
        const value = e.target.value;
        if (/^(\d*\.?\d*)$/.test(value) || value === '') {
            setAmount(value);
        }
    };

    const handleClosePosition = async (positionSymbol: string) => {
        setClosePositionLoading(prev => ({ ...prev, [positionSymbol]: true }));
        try {
            await closePosition(positionSymbol);
            success({
                title: 'Position Closed',
                message: `Successfully closed position for ${formatSymbol(positionSymbol)}.`
            });
        } catch (e: any) {
            error({
                title: 'Position Closure Failed',
                message: `Failed to close position for ${formatSymbol(positionSymbol)}: ${e.message}.`
            });
        } finally {
            await handleFinish();
            setClosePositionLoading(prev => ({ ...prev, [positionSymbol]: false }));
        }
    };

    const handleBuy = async () => {
        setBuyLoading(true);
        try {
            await placeMarketOrder({
                symbol,
                side: 'buy',
                usdAmount: parseFloat(amount)
            });
            success({
                title: 'Buy Order Placed',
                message: `Successfully placed buy order for ${amount} USD of ${formatSymbol(symbol)}.`
            });
        } catch (e: any) {
            error({
                title: 'Buy Order Failed',
                message: `Failed to place buy order for ${amount} USD of ${formatSymbol(symbol)}: ${e.message}.`
            });
        } finally {
            await handleFinish();
            setBuyLoading(false);
        }
    };

    const handleSell = async () => {
        setSellLoading(true);
        try {
            await placeMarketOrder({
                symbol,
                side: 'sell',
                usdAmount: parseFloat(amount)
            });
            success({
                title: 'Sell Order Placed',
                message: `Successfully placed sell order for ${amount} USD of ${formatSymbol(symbol)}.`
            });
        } catch (e: any) {
            error({
                title: 'Sell Order Failed',
                message: `Failed to place sell order for ${amount} USD of ${formatSymbol(symbol)}: ${e.message}.`
            });
        } finally {
            await handleFinish();
            setSellLoading(false);
        }
    };

    const handleReverse = async () => {
        setReverseLoading(true);
        const position = positions.value.find(p => p.symbol === symbol);
        if (!position) {
            error({
                title: 'Reverse Order Failed',
                message: `No position found for ${formatSymbol(symbol)}.`
            });
            setReverseLoading(false);
            return;
        }
        if (!amount) {
            error({
                title: 'Reverse Order Failed',
                message: `Please enter an amount.`
            });
            setReverseLoading(false);
            return;
        }

        try {
            await closePosition(symbol);
            await placeMarketOrder({
                symbol,
                side: position.side === 'long' ? 'sell' : 'buy',
                usdAmount: parseFloat(amount)
            });
            success({
                title: 'Reverse Order Placed',
                message: `Successfully reversed position to ${amount} USD of ${formatSymbol(symbol)}.`
            });
        } catch (e: any) {
            error({
                title: 'Reverse Order Failed',
                message: `Failed to reverse position for ${amount} USD of ${formatSymbol(symbol)}: ${e.message}.`
            });
        } finally {
            await handleFinish();
            setReverseLoading(false);
        }
    };

    const handleFinish = async () => {
        await reloadAccountData().catch(e => console.error(e));
        setAmount('');
    }

    return (
        <TradingPanelContainer
            ref={panelRef}
            style={{
                top: defaultPosition.y,
                left: defaultPosition.x,
            }}
        >
            <TradingPanelMainContainer>
                <TradingPanelHeader onMouseDown={handleMouseDown}>
                    <span>Trade {formatSymbol(symbol)} </span>
                    <span style={{ fontSize: '0.6em', marginLeft: 3, paddingTop: '0.6em' }}>x{leverage}</span>

                    {availableMargin && (
                        <span style={{ fontSize: '0.85em', position: 'absolute', right: '7%', opacity: 0.8 }}>
                            ${parseFloat(availableMargin).toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
                        </span>
                    )}
                </TradingPanelHeader>

                <InputContainer>
                    <AmountInput
                        type="text"
                        placeholder={`Amount (USD)  Avail.${availableMargin ? (parseFloat(availableMargin) * leverage).toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 }) : ''}`}
                        aria-label="Trading amount in USD"
                        value={amount}
                        onChange={handleAmountChange}
                    />
                </InputContainer>
                <ButtonsContainer>
                    <ActionButton
                        action="buy"
                        onClick={handleBuy}
                        loading={buyLoading}
                        disabled={buyLoading}
                    >
                        Buy
                    </ActionButton>
                    <ActionButton
                        action="sell"
                        onClick={handleSell}
                        loading={sellLoading}
                        disabled={sellLoading}
                    >
                        Sell
                    </ActionButton>
                    <ActionButton
                        action="reverse"
                        onClick={handleReverse}
                        loading={reverseLoading}
                        disabled={reverseLoading}
                    >
                        Rev.
                    </ActionButton>
                </ButtonsContainer>

            </TradingPanelMainContainer>



            <PositionsContainer open={isOpen.value}>
                <PositionsList>
                    {
                        positions.value.map((position, index) => (
                            <PositionItem key={`${position.symbol}-${index}`} side={position.side as any} onClick={() => { navigateTo(`/${position.symbol}-${timeframe}`) }}>
                                <PositionDetails >
                                    <PositionSymbol>
                                        {formatSymbol(position.symbol)}
                                        <PositionSide side={position.side as any}>
                                            {position.side === EPositionSide.Long ? 'L' : 'S'}
                                        </PositionSide>
                                        <PositionAmount>
                                            &nbsp; (≈${position.usdAmount})
                                        </PositionAmount>
                                    </PositionSymbol>

                                    <PositionPnl status={position.unrealizedPnl > '0' ? 'positive' : 'negative'}>
                                        {position.unrealizedPnl > '0' ? `+${position.unrealizedPnl}` : position.unrealizedPnl}
                                        &nbsp; ({position.unrealizedPnl > '0' ? '↑' : '↓'}
                                        &nbsp;{(parseFloat(position.pnlPct) * 100).toFixed(2).replace('-', '')}%)
                                    </PositionPnl>
                                </PositionDetails>
                                <ActionButton
                                    action="close"
                                    onClick={() => handleClosePosition(position.symbol)}
                                    disabled={closePositionLoading[position.symbol] ?? false}
                                    loading={closePositionLoading[position.symbol] ?? false}
                                >
                                    Close
                                </ActionButton>
                            </PositionItem>
                        ))}
                </PositionsList>
            </PositionsContainer>
        </TradingPanelContainer>
    );
};


