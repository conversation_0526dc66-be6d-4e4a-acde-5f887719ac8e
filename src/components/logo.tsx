import { <PERSON>go<PERSON>ontainer, ConcentricR<PERSON>t, LogoText, LogoChar } from "../styles/logoStyles"

export const Logo = () => {
    return <LogoContainer>
        <ConcentricRect level={1} />
        <ConcentricRect level={2} />
        <ConcentricRect level={3} />
        <ConcentricRect level={4} />
        <LogoText>
            {/* Create a separate LogoChar for each letter with different animation delays */}
            <LogoChar position={0}>S</LogoChar>
            <LogoChar position={1}>o</LogoChar>
            <LogoChar position={2}>l</LogoChar>
            <LogoChar position={3}>a</LogoChar>
            <LogoChar position={4}>r</LogoChar>
            <LogoChar position={5}>i</LogoChar>
            <LogoChar position={6}>s</LogoChar>
        </LogoText>
    </LogoContainer>
}