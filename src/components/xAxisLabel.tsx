import { Label, Tag, Text } from "react-konva";
import { useEffect, useRef, useState } from "react";
import Konva from "konva";
import { useXAxisScale } from "../hooks/useScale";
import { format } from "date-fns";
import { AXIS_LABEL_COLOR, } from "../commons/constants";
import { isNil } from "../commons/util";


export default function XAxisLabel(props: {
    visible?: boolean,
    time?: number,
    x?: number,
    fontColor?: string,
    backgroundColor?: string,
    borderColor?: string,
    y?: number
}) {
    const { x2t, t2x } = useXAxisScale();
    const labelRef = useRef<Konva.Label>(null);
    const [labelX, setLabelX] = useState(0);
    const [labelText, setLabelText] = useState('');

    useEffect(() => {
        const labelWidth = labelRef.current && labelRef.current.getText() ? labelRef.current.getText().width() : 0;
        if (!labelRef.current || !props.visible) return;
        if (!isNil(props.x)) {
            setLabelX(props.x - labelWidth / 2);
            setLabelText(format(x2t(props.x), 'MM/dd HH:mm'));
        } else if (!isNil(props.time)) {
            setLabelX(t2x(props.time) - labelWidth / 2);
            setLabelText(format(x2t(props.time), 'MM/dd HH:mm'));
        }
    }, [labelRef.current, props.time, props.x, t2x, x2t]);

    return (
        <Label
            ref={labelRef}
            x={labelX}
            y={props.y ?? 0}
            visible={props.visible !== false}
            perfectDrawEnabled={false}
        >
            <Tag
                fill={props.backgroundColor || AXIS_LABEL_COLOR}
                stroke={props.borderColor || AXIS_LABEL_COLOR}
                strokeWidth={1}
                cornerRadius={12}
                shadowColor="rgba(0, 0, 0, 0.25)"
                shadowBlur={3}
                shadowOffsetY={1}
                shadowOffsetX={0}
            />
            <Text
                text={labelText}
                padding={8}
                fill={props.fontColor || 'white'}
                fontSize={12}
                fontStyle="bold"
            />
        </Label>
    );
}