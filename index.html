<!doctype html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <link rel="icon" type="image/png" href="/solaris.png" />
  <link rel="apple-touch-icon" href="/solaris.png" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <meta name="theme-color" content="#ffffff" />
  <meta name="apple-mobile-web-app-capable" content="yes" />
  <meta name="apple-mobile-web-app-status-bar-style" content="default" />
  <meta name="apple-mobile-web-app-title" content="Solaris" />
  <link rel="manifest" href="/manifest.json" />
  <title>Solaris</title>
  <style>
    @font-face {
      font-family: 'Zed';
      src: url('/src/assets/zed.woff2') format('woff2');
      font-weight: normal;
      font-style: normal;
      font-display: swap;
    }

    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
      font-family: 'Zed', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
    }

    html,
    body {
      width: 100%;
      height: 100%;
      overflow: hidden;
    }

    #main {
      width: 100%;
      height: 100%;
    }
  </style>
</head>

<body>
  <div id="main">
  </div>
  <svg style="display: none;">
    <!-- Main Filter for default state -->
    <filter id="glass-distortion-with-blur" x="0%" y="0%" width="100%" height="100%" filterUnits="objectBoundingBox"
      color-interpolation-filters="sRGB">
      <!-- STEP 1: Apply a blur to the SourceGraphic (the element itself) -->
      <feGaussianBlur in="SourceGraphic" stdDeviation="8" result="initialBlur" />

      <!-- STEP 2: Generate Perlin noise for distortion.
                 Increased baseFrequency, especially in Y, and more octaves create a more
                 stretched and complex, 'bending light' like noise pattern. -->
      <feTurbulence type="fractalNoise" baseFrequency="0.01 0.01" numOctaves="1" seed="5" result="turbulenceNoise" />

      <!-- STEP 3: Blurs the noise to create smoother transitions for displacement -->
      <feGaussianBlur in="turbulenceNoise" stdDeviation="5" result="blurredNoise" />

      <!-- STEP 4: Distorts the initially blurred graphic based on the R and G channels of blurredNoise.
                 Significantly increased 'scale' for a more intense, twisted, and stretched effect. -->
      <feDisplacementMap in="initialBlur" in2="blurredNoise" scale="150" xChannelSelector="R" yChannelSelector="G" />
    </filter>
  </svg>

  <script type="module" src="/src/main.tsx"></script>
</body>

</html>